@student = User.find_by_canvas_id(4)
context_codes = %w[course_2 user_4 course_1]
calendar_params = { context_codes: context_codes }
Calendar::EventsService.new(@student.canvas_id, calendar_params).call.to_a


calendar_params = { context_codes: context_codes }
Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params).call.to_a

calendar_params = { context_codes: context_codes }
Calendar::ItemsService.new(@student.canvas_id, calendar_params).call.to_a



calendar_params = { context_codes: context_codes, undated: true, type: 'assignment' }
Calendar::EventsService.new(@student.canvas_id, calendar_params).call.to_a

calendar_params = { context_codes: context_codes, undated: true, type: 'sub_assignment' }
Calendar::EventsService.new(@student.canvas_id, calendar_params).call.to_a


calendar_params = { context_codes: context_codes, undated: true }
Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params).call.to_a

calendar_params = { context_codes: context_codes, undated: true }
Calendar::ItemsService.new(@student.canvas_id, calendar_params).call.to_a
