// See the shakacode/shakapacker README and docs directory for advice on customizing your webpackConfig.
const { generateWebpackConfig } = require('shakapacker')

const webpack = require("webpack")
const path = require("path")

const webpackConfig = generateWebpackConfig(
  {
    plugins: [
        new webpack.ProvidePlugin({
            React: 'react',
        }),

        new webpack.DefinePlugin({
            "process.env.ALWAYS_APPEND_UI_TESTABLE_LOCATORS": 'true',
            "process.env.OMIT_INSTUI_DEPRECATION_WARNINGS": 'true',
        }),

    ].filter(<PERSON>olean),
    resolve: {
        alias: {
          '@shared': path.resolve(__dirname, '../..', 'app/javascript/shared'),
          '@utils': path.resolve(__dirname, '../..', 'app/javascript/utils'),
          '@': path.resolve(__dirname, '../..', 'app/javascript'),
          '@toolkit': path.resolve(__dirname, '../../node_modules/@inst_proserv/toolkit/esm')
        }
      }
  }
)



module.exports = webpackConfig