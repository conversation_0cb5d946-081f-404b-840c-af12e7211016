PandaPal.lti_options = {
  title: 'Student Observer',
  lti_spec_version: 'v1p3',
  settings_structure: {
    canvas: {
      type: 'Hash',
      required: true,
      properties: {
        base_url: { type: 'String', required: true },
        api_token: { type: 'String', required: true },
        default_time_zone: { type: 'String' },
        external_tool_id: { type: 'Integer' }
      }
    }
  }
}

PandaPal.lti_environments = {
  domain: ENV.fetch('PROD_DOMAIN', nil),
  beta_domain: ENV.fetch('BETA_DOMAIN', nil),
  test_domain: ENV.fetch('TEST_DOMAIN', nil)
}

PandaPal.lti_custom_params = {
  custom_canvas_user_id: '$Canvas.user.id',
  custom_canvas_role: '$Canvas.membership.roles',
  custom_canvas_course_id: '$Canvas.course.id',
  custom_canvas_account_id: '$Canvas.account.id',
  custom_canvas_user_locale: '$Message.locale',
  custom_canvas_user_timezone: '$Person.address.timezone',
  custom_canvas_shard_id: '$Canvas.shard.id',
  custom_canvas_observee_ids: '$com.instructure.User.observees',
}

PandaPal.stage_navigation(
  :course_navigation,
  enabled: true,
  text: 'Student Observer',
  display_type: 'full_width_in_context'
)


PandaPal.stage_navigation(
  :account_navigation,
  enabled: true,
  text: 'Student Observer',
  display_type: 'full_width_in_context'
)
