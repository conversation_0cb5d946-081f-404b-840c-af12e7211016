require 'sidekiq'
require 'sidekiq-scheduler'

# Only set adapter if not already set (avoids overwriting test config)
Rails.application.config.active_job.queue_adapter ||= :sidekiq

redis_url = ENV.fetch('REDIS_URL', 'redis://localhost:6379')

Sidekiq.configure_server do |config|
  config.redis = { url: redis_url }

  config.server_middleware do |chain|
    chain.add Apartment::Sidekiq::Middleware::Server
    chain.add Sidekiq::Batch::Server
    # chain.add WorkerCallbacks::Middleware::Server
  end
end

Sidekiq.configure_client do |config|
  config.redis = { url: redis_url }
end
