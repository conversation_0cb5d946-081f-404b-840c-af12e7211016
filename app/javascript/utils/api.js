import axios from './axios'

export const getStudents = (params = {}) => {
  return axios.get('/students', { params })
}

export const createObserverLink = (studentId) => {
  return axios.post(`students/${studentId}/create_observer_link`)
}

export const getCurrentObserverLink = () => {
  return axios.get('observer_link/current')
}

export const renewObserverLink = (id, params = {}) => {
  return axios.patch(`observer_link/renew/${id}`, params)
}

export const endObserverLink = (id, params = {}) => {
  return axios.delete(`observer_link/end/${id}`, { params })
}
