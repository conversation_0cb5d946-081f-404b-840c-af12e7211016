import React from 'react'
import { Table, Text, Flex, Spinner, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@instructure/ui'
import {
  IconMoreSolid,
  IconMiniArrowUpLine,
  IconMiniArrowDownLine
} from '@instructure/ui-icons'
import { Pagination } from '@instructure/ui-pagination'

const StudentsTable = ({
  students,
  loading,
  currentPage,
  setCurrentPage,
  totalPages,
  sortBy,
  ascending,
  handleSort,
  onCreateObserverLink,
  isFiltered = false,
  totalResults = 0,
  searchQuery = ''
}) => {
  const renderSortIcon = (column) => {
    if (sortBy === column) {
      return ascending ? <IconMiniArrowUpLine /> : <IconMiniArrowDownLine />
    }
    return null
  }

  const renderTableHeaders = () => (
    <Table.Row>
      <Table.ColHeader
        id="student-name"
        onClick={() => handleSort('users.sortable_name')}
        as="button"
        textAlign="start"
        width={'80%'}
      >
        <Text color="alert" size="medium" weight="weightRegular">
          Student Name A-Z {renderSortIcon('users.sortable_name')}
        </Text>
      </Table.ColHeader>
      <Table.ColHeader id="contact-info" padding="small">
        <Text color="alert" size="medium" weight="weightRegular">
          SIS ID
        </Text>
      </Table.ColHeader>
      <Table.ColHeader id="contact-info" textAlign="end">
        <Text color="alert" size="medium" weight="weightRegular">
          Actions
        </Text>
      </Table.ColHeader>
    </Table.Row>
  )

  const renderTableBody = () =>
    students.map((student) => (
      <Table.Row key={student.canvas_id} hover={false}>
        <Table.Cell>
          <Flex direction="row" align="center" padding="xx-small 0">
            <Flex.Item margin="0 small 0 0">
              <Text weight="weightRegular" size="medium" color="brand">
                {student.sortable_name}
              </Text>
            </Flex.Item>
          </Flex>
        </Table.Cell>

        <Table.Cell>
          <Text
            size="small"
            margin="0 small"
            color="brand"
            weight="weightRegular"
          >
            {student.sis_id}
          </Text>
        </Table.Cell>

        <Table.Cell textAlign="end">
          <Menu
            trigger={
              <span role="button" tabIndex={0} style={{ cursor: 'pointer' }}>
                <IconMoreSolid />
              </span>
            }
            placement="bottom"
          >
            <Menu.Item
              onSelect={() =>
                onCreateObserverLink && onCreateObserverLink(student)
              }
            >
              Create temporary observer link
            </Menu.Item>
          </Menu>
        </Table.Cell>
      </Table.Row>
    ))

  if (loading) {
    return (
      <Flex alignItems="center" justifyItems="center" padding="xx-large">
        <Spinner renderTitle="Loading students..." />
      </Flex>
    )
  }

  return (
    <>
      {isFiltered && (
        <Text size="small" margin="0 0 medium 0">
          Showing {totalResults} result{totalResults !== 1 ? 's' : ''} for "
          {searchQuery}"
        </Text>
      )}

      <Table caption={false} layout="auto" hover>
        <Table.Head>{renderTableHeaders()}</Table.Head>
        <Table.Body>{renderTableBody()}</Table.Body>
      </Table>

      {!isFiltered && totalPages > 1 && (
        <View
          as="div"
          padding="small"
          margin="medium 0 0 0"
          position="fixed"
          insetInlineStart="0"
          insetBlockEnd="0"
        >
          <Pagination
            as="nav"
            margin="large small small"
            variant="compact"
            labelNext="Next Page"
            labelPrev="Previous Page"
            labelFirst="First Page"
            labelLast="Last Page"
            currentPage={currentPage}
            onPageChange={(nextPage) => setCurrentPage(nextPage)}
            totalPageNumber={totalPages}
            siblingCount={3}
            boundaryCount={2}
          />
        </View>
      )}
    </>
  )
}

export default StudentsTable
