import React from 'react'
import { Flex } from '@instructure/ui-flex'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'

const APP_NAME = 'STRIDE STUDENT OBSERVER'

const Header = () => {
  return (
    <View as="header">
      <Flex justifyItems="space-between" alignItems="center">
        <Flex.Item shouldGrow>
          <Text
            transform="uppercase"
            size="x-small"
            letterSpacing="expanded"
            weight="normal"
            color="primary"
          >
            {APP_NAME}
          </Text>
        </Flex.Item>
      </Flex>
    </View>
  )
}

export default Header
