import React from 'react'
import { View, Flex, Text, Alert, Heading } from '@instructure/ui'
import { IconClockLine } from '@instructure/ui-icons'
import { formatTimeRemaining, getStatusVariant } from '../../utils/timeUtils'
import { useCountdownTimer } from './CountdownTimer'
import ObserverLinkFooter from './ObserverLinkFooter'
import CalendarLayout from './calendar/CalendarLayout';

const StudentCalendar = ({
  observerLink,
  onEndLink,
  onRenewLink,
  onExpired,
  loading = false
}) => {
  const timeRemaining = useCountdownTimer(observerLink?.expires_at, onExpired)

  if (!observerLink) return null

  return (
    <View>
      <Flex justifyItems="space-between" margin="0 0 small 0"></Flex>

      {timeRemaining <= 120 && (
        <Alert
          variant={getStatusVariant(timeRemaining)}
          margin="0 0 large 0"
          renderCloseButtonLabel="Close"
          timeout={5000}
        >
          <Flex direction="column" gap="small">
            <Text weight="bold">
              {timeRemaining <= 0
                ? 'Observer Link Expired'
                : 'Active Observer Link'}
            </Text>
            <Flex alignItems="center" gap="small">
              <IconClockLine />
              <Text>Time remaining: {formatTimeRemaining(timeRemaining)}</Text>
            </Flex>
          </Flex>
        </Alert>
      )}
      <View as="div" padding="medium">
        <Flex direction="column">
          <Flex alignItems="center" gap="small">
            <Flex direction="column" gap="x-small">
              <Text weight="weightRegular" size="large">
                {observerLink.observed_student.sortable_name}
              </Text>
              <Text size="small">
                SIS ID: {observerLink.observed_student.sis_id || 'N/A'}
              </Text>

              <View display="block">
                <Heading
                  level="h2"
                  as="h2"
                  themeOverride={{ h2FontWeight: 600 }}
                >
                  Student Calendar{' '}
                  <Text weight="normal" color="secondary">
                    {' '}
                    Read-Only
                  </Text>
                </Heading>
              </View>
            </Flex>
          </Flex>
        </Flex>
        <View
          as="div"
          margin="small 0 0 0"
          borderWidth="0 0 small 0"
          borderColor="secondary"
        />
      </View>
      <View>
        <CalendarLayout />
      </View>

      <ObserverLinkFooter
        observerLink={observerLink}
        timeRemaining={timeRemaining}
        onEndLink={onEndLink}
        onRenewLink={onRenewLink}
        loading={loading}
      />
    </View>
  )
}

export default StudentCalendar
