import React, { useState, useEffect } from 'react'
import { calculateTimeRemaining } from '../../utils/timeUtils'

/**
 * Custom hook for managing countdown timer
 * @param {string|Date} expiresAt - The expiration date/time
 * @param {Function} onExpired - Callback function when timer expires
 * @returns {number} - Current time remaining in seconds
 */
export const useCountdownTimer = (expiresAt, onExpired) => {
  const [timeRemaining, setTimeRemaining] = useState(
    calculateTimeRemaining(expiresAt)
  )

  useEffect(() => {
    if (!expiresAt) return

    const timer = setInterval(() => {
      const remaining = calculateTimeRemaining(expiresAt)

      // Check if timer just expired
      if (timeRemaining > 0 && remaining === 0 && onExpired) {
        onExpired()
      }

      setTimeRemaining(remaining)
    }, 1000)

    return () => clearInterval(timer)
  }, [expiresAt, timeRemaining, onExpired])

  return timeRemaining
}
