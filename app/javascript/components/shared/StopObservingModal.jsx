import React, { useState, useEffect } from 'react'
import {
  Mo<PERSON>,
  <PERSON><PERSON>,
  Text,
  Flex,
  Spinner,
  Alert,
  CloseButton,
  View
} from '@instructure/ui'

const StopObservingModal = ({
  isOpen,
  onClose,
  observerLink,
  onConfirm,
  loading = false
}) => {
  const [error, setError] = useState(null)
  const [timeObserved, setTimeObserved] = useState(null)

  useEffect(() => {
    if (!observerLink) return
    if (!isOpen) return

    const getObservedString = () => {
      const startedAt = new Date(observerLink.created_at).getTime()
      const now = Date.now()
      const elapsed = Math.max(0, Math.floor((now - startedAt) / 1000))
      const minutes = Math.floor(elapsed / 60)
      const seconds = elapsed % 60

      return `${minutes}.${seconds.toString().padStart(2, '0')} sec`
    }

    setTimeObserved(getObservedString())

    const interval = setInterval(() => {
      setTimeObserved(getObservedString())
    }, 1000)

    return () => clearInterval(interval)
  }, [isOpen, observerLink])

  const handleConfirm = async () => {
    try {
      setError(null)
      await onConfirm()
    } catch (err) {
      const errorMessage =
        err.response?.data?.error || err.message || 'Failed to stop observing'
      setError(errorMessage)
    }
  }

  const handleClose = () => {
    setError(null)
    onClose()
  }

  if (!observerLink) return null

  return (
    <Modal
      open={isOpen}
      onDismiss={handleClose}
      size="small"
      label="Stop Observing"
      shouldCloseOnDocumentClick={false}
    >
      <Modal.Header>
        <Text size="large" weight="bold">
          Are you sure you want to stop observing?
        </Text>
        <CloseButton
          placement="end"
          offset="small"
          onClick={handleClose}
          screenReaderLabel="Close"
        />
      </Modal.Header>

      <Modal.Body>
        <Flex direction="column" gap="medium">
          {error && (
            <Alert
              variant="error"
              margin="0 0 small 0"
              renderCloseButtonLabel="Close"
              timeout={3000}
              onDismiss={() => setError(null)}
            >
              {error}
            </Alert>
          )}

          <Text>
            There is still time remaining to observe this student. Are you sure
            to stop observing?
          </Text>

          <View margin="medium 0">
            <Flex justifyItems="space-between" padding="0 0 small 0">
              <Flex.Item>
                <Text weight="bold" color="secondary">
                  Name
                </Text>
              </Flex.Item>
              <Flex.Item>
                <Text weight="bold" color="secondary">
                  Time Observed
                </Text>
              </Flex.Item>
              <Flex.Item>
                <Text weight="bold" color="secondary">
                  SIS ID
                </Text>
              </Flex.Item>
            </Flex>

            <View
              as="div"
              borderWidth="0 0 small 0"
              borderColor="secondary"
              margin="0 0 small 0"
            />

            <Flex justifyItems="space-between" padding="small 0 0 0">
              <Flex.Item>
                <Text size="medium">
                  {observerLink.observed_student.sortable_name}
                </Text>
              </Flex.Item>
              <Flex.Item>
                <Text size="medium">{timeObserved}</Text>
              </Flex.Item>
              <Flex.Item>
                <Text size="medium">
                  {observerLink.observed_student.sis_id || 'N/A'}
                </Text>
              </Flex.Item>
            </Flex>
          </View>
        </Flex>
      </Modal.Body>

      <Modal.Footer>
        <Button onClick={handleClose} margin="0 x-small 0 0" disabled={loading}>
          Cancel
        </Button>
        <Button color="danger" onClick={handleConfirm} disabled={loading}>
          {loading ? (
            <Flex alignItems="center" gap="x-small">
              <Spinner size="x-small" renderTitle="Stopping..." />
              <Text>Stopping...</Text>
            </Flex>
          ) : (
            'Stop Observing'
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default StopObservingModal
