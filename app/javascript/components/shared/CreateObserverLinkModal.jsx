import React, { useState } from 'react'
import {
  <PERSON>dal,
  <PERSON><PERSON>,
  Text,
  Flex,
  Alert,
  CloseButton,
  View
} from '@instructure/ui'

const CreateObserverLinkModal = ({
  isOpen,
  onClose,
  student,
  onConfirm,
  loading = false
}) => {
  const [error, setError] = useState(null)

  const handleConfirm = async () => {
    try {
      setError(null)
      await onConfirm(student)
    } catch (err) {
      const errorMessage =
        err.response?.data?.error ||
        err.message ||
        'Failed to create observer link'
      setError(errorMessage)
    }
  }

  const handleClose = () => {
    setError(null)
    onClose()
  }

  if (!student) return null

  return (
    <Modal
      open={isOpen}
      onDismiss={handleClose}
      size="small"
      label="Create Temporary Observer Link"
      shouldCloseOnDocumentClick={false}
    >
      <Modal.Header>
        <Text size="large" weight="bold">
          Create a Temporary Observer Link
        </Text>
        <CloseButton
          placement="end"
          offset="small"
          onClick={handleClose}
          screenReaderLabel="Close"
        />
      </Modal.Header>

      <Modal.Body>
        <Flex direction="column" gap="medium">
          {error && (
            <Alert
              variant="error"
              margin="0 0 small 0"
              renderCloseButtonLabel="Close"
              timeout={3000}
            >
              {error}
            </Alert>
          )}
          <Text size="small">
            Creating a Temporary Observer Link allows you temporary viewing of
            this student's courses for 1 hour. Link will expire after 1 hour and
            can be renewed only once.
          </Text>

          <View margin="medium 0">
            <Flex justifyItems="space-between" padding="0 0 small 0">
              <Flex.Item>
                <Text weight="bold">Student Name</Text>
              </Flex.Item>
              <Flex.Item>
                <Text weight="bold">SIS ID</Text>
              </Flex.Item>
            </Flex>

            <View
              as="div"
              borderWidth="0 0 small 0"
              borderColor="secondary"
              margin="0 0 small 0"
            />

            <Flex justifyItems="space-between" padding="small 0 0 0">
              <Flex.Item>
                <Text size="medium">{student.sortable_name}</Text>
              </Flex.Item>
              <Flex.Item>
                <Text size="medium">{student.sis_id || 'N/A'}</Text>
              </Flex.Item>
            </Flex>
          </View>
        </Flex>
      </Modal.Body>

      <Modal.Footer>
        <Button onClick={handleClose} margin="0 x-small 0 0" disabled={loading}>
          Cancel
        </Button>
        <Button color="primary" onClick={handleConfirm} disabled={loading}>
          {loading ? (
            <Flex alignItems="center" gap="x-small">
              <Text>Loading...</Text>
            </Flex>
          ) : (
            'Start Observing'
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default CreateObserverLinkModal
