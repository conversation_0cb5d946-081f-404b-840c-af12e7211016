import React, { useState, useRef } from 'react'
import { TextInput } from '@instructure/ui-text-input'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import { IconButton } from '@instructure/ui-buttons'
import { View } from '@instructure/ui-view'
import { ScreenReaderContent } from '@instructure/ui-a11y-content'

const StudentSearchInput = ({ onSearchChange, searchValue = '' }) => {
  const [value, setValue] = useState(searchValue)
  const inputRef = useRef(null)

  const handleChange = (_, val) => {
    setValue(val)
    if (onSearchChange) {
      onSearchChange(val)
    }
  }

  const handleClear = (e) => {
    e.stopPropagation()
    setValue('')
    if (onSearchChange) {
      onSearchChange('')
    }
    inputRef.current?.focus()
  }

  const renderClearButton = () => {
    if (!value.length) return null
    return (
      <IconButton
        type="button"
        size="small"
        withBackground={false}
        withBorder={false}
        screenReaderLabel="Clear search"
        onClick={handleClear}
      >
        <IconXLine />
      </IconButton>
    )
  }

  return (
    <View as="div">
      <TextInput
        renderLabel={<ScreenReaderContent>Search Students</ScreenReaderContent>}
        placeholder="Search"
        width="20rem"
        value={value}
        onChange={handleChange}
        inputRef={(el) => (inputRef.current = el)}
        renderBeforeInput={<IconSearchLine inline={false} />}
        renderAfterInput={renderClearButton()}
      />
    </View>
  )
}

export default StudentSearchInput
