import React, { useEffect, useState } from 'react';
import { Grid } from '@instructure/ui-grid';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { TruncateText } from '@instructure/ui-truncate-text'

const getMonthlyEventsAPI = (startDate, endDate) => {
  // API call to fetch events based on the start and end date
  console.log("Fetching events from", startDate, "to", endDate);
  return [
    {
        "id": "4",
        "title": "Course GPB4 event test1",
        "start_at": "2025-09-20T12:00:00Z",
        "end_at": "2025-09-21T01:00:00Z",
        "location_name": "",
        "type": "event",
        "description": null,
        "context_name": "GPB4"
    },
    {
        "id": "6",
        "title": "Another Test Cal Event for override month",
        "start_at": "2025-09-20T22:00:00Z",
        "end_at": "2025-09-20T00:00:00Z",
        "location_name": "Some where in this world",
        "type": "event",
        "description": null,
        "context_name": "Albert Einstein",
    },    
    {
        "id": "5",
        "title": "Test Cal Event for override month",
        "start_at": "2025-09-03T22:00:00Z",
        "end_at": "2025-09-04T00:00:00Z",
        "location_name": "Some where in this world",
        "type": "event",
        "description": null,
        "context_name": "Albert Einstein",
    }
  ];
}

const MonthView = ({ daysOfWeek, meta, today }) => {
  const [userEvents, setUserEvents] = useState([]);

  const getCalendarCells = (meta) => {
    const cells = [];
    const pad = (n) => String(n).padStart(2, '0');
    // Previous month days
    for (let i = 0; i < meta.startDayIdx; i++) {
      let year = meta.month === 0 ? meta.year - 1 : meta.year;
      let month = meta.month === 0 ? 11 : meta.month - 1;
      let day = meta.prevMonthLastDay - meta.startDayIdx + i + 1;
      let fullDate = `${year}-${pad(month + 1)}-${pad(day)}`;
      cells.push({ day, type: 'prev', fullDate });
    }
    // Current month days
    for (let d = 1; d <= meta.daysInMonth; d++) {
      let fullDate = `${meta.year}-${pad(meta.month + 1)}-${pad(d)}`;
      cells.push({ day: d, type: 'current', fullDate });
    }
    // Next month days
    let nextDay = 1;
    let nextMonth = meta.month === 11 ? 0 : meta.month + 1;
    let nextYear = meta.month === 11 ? meta.year + 1 : meta.year;
    // Fill up to next multiple of 7 (35 or 42)
    const totalCells = cells.length % 7 === 0 ? cells.length : cells.length + (7 - (cells.length % 7));

    while (cells.length < totalCells) {
      let fullDate = `${nextYear}-${pad(nextMonth + 1)}-${pad(nextDay)}`;
      cells.push({ day: nextDay++, type: 'next', fullDate });
    }
    return cells;
  };

  const getCurrentGridStartEndDates = (cells, meta) => {
    const pad = (n) => String(n).padStart(2, '0');
    let startYear, startMonth;
    if (cells[0].type === 'prev') {
      startYear = meta.month === 0 ? meta.year - 1 : meta.year;
      startMonth = meta.month === 0 ? 11 : meta.month - 1;
    } else {
      startYear = meta.year;
      startMonth = meta.month;
    }
    let endYear, endMonth;
    if (cells[cells.length - 1].type === 'next') {
      endYear = meta.month === 11 ? meta.year + 1 : meta.year;
      endMonth = meta.month === 11 ? 0 : meta.month + 1;
    } else {
      endYear = meta.year;
      endMonth = meta.month;
    }
    const startDate = `${startYear}-${pad(startMonth + 1)}-${pad(cells[0].day)}`;
    const endDate = `${endYear}-${pad(endMonth + 1)}-${pad(cells[cells.length - 1].day)}`;
    return { startDate, endDate };
  };

  const renderEventItem = (event) => {
    return (
      <View
        key={event.id}
        as="div"
        borderColor="brand"
        borderWidth="small"
        borderStyle="solid"
        borderRadius="medium"
        padding="xx-small"
        margin="xxx-small"
        maxWidth="90%"
      >
        <Text color='brand' variant="contentSmall">
          <TruncateText>
            {event.title}
           </TruncateText> 
          </Text>
      </View>
    );
  };

  const renderDayCell = (cell, today, meta) => {
    const isToday = cell.type === 'current' && cell.day === today.getDate() && meta.month === today.getMonth() && meta.year === today.getFullYear();
    const gridBackground = isToday && cell.type === 'current' ? "secondary" : "primary";
    // Find events for this cell
    const eventsForCell = userEvents.filter(ev => {
      const eventDate = ev.start_at.slice(0, 10); // 'YYYY-MM-DD'
      return eventDate === cell.fullDate;
    });

    return (
      <View
        as="div"
        display="block"
        borderWidth="small"
        borderStyle="solid"
        borderColor="primary"
        borderRadius={cell.type === 'current' ? "medium" : "none"}
        background={gridBackground}
        minWidth="10rem"
        minHeight="10rem"
        padding="none"
      >
        <Text 
          color={cell.type === 'current' ? undefined : "secondary"}
          themeOverride={{ secondaryColor: '#898e93' }}
        >
          {cell.day}
        </Text>
        {eventsForCell.map(ev => (
          renderEventItem(ev)
        ))}
      </View>
    );
  };

  const cells = getCalendarCells(meta);
  const numRows = Math.ceil(cells.length / 7);
  const { startDate, endDate } = getCurrentGridStartEndDates(cells, meta);

  useEffect(() => {
    if (typeof getMonthlyEventsAPI === 'function') {
      const response = getMonthlyEventsAPI(startDate, endDate);
      setUserEvents(response);
    }
  }, [meta.year, meta.month]);

  return (
    <Grid colSpacing="none" rowSpacing="none">
      <Grid.Row>
        {daysOfWeek.map((day, idx) => (
          <Grid.Col key={idx}>
            <View as="div" textAlign="center" padding="small">
              <Text>{day}</Text>
            </View>
          </Grid.Col>
        ))}
      </Grid.Row>
      {[...Array(numRows)].map((_, rowIdx) => (
        <Grid.Row key={rowIdx}>
          {cells.slice(rowIdx * 7, (rowIdx + 1) * 7).map((cell, colIdx) => (
            <Grid.Col key={colIdx}>
              {renderDayCell(cell, today, meta)}
            </Grid.Col>
          ))}
        </Grid.Row>
      ))}
    </Grid>
  );
};

export default MonthView;
