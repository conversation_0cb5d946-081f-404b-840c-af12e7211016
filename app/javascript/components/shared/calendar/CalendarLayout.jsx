import React, { useState } from 'react';
import { Grid } from '@instructure/ui-grid';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Flex, FlexItem } from '@instructure/ui-flex';
import { Button } from '@instructure/ui-buttons';
import { IconArrowStartLine, IconArrowEndLine } from '@instructure/ui-icons';
import { Calendar } from '@instructure/ui-calendar'
import MonthView from './MonthView';
import WeekView from './WeekView';
import AgendaView from './AgendaView';

const CalendarLayout = () => {
  const daysOfWeek = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];

  const getCalendarMeta = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startDayIdx = (firstDay.getDay() + 6) % 7; // 0=Mon, 6=Sun
    const prevMonthLastDay = new Date(year, month, 0).getDate();
    return { year, month, daysInMonth, startDayIdx, prevMonthLastDay };
  };

  const today = new Date();
  const [displayedDate, setDisplayedDate] = useState(new Date(today.getFullYear(), today.getMonth(), 1));
  const [viewType, setViewType] = useState('month');
  const [weekIndex, setWeekIndex] = useState(0); // 0-based week index for week view
  const meta = getCalendarMeta(displayedDate);


  const handlePrevMonth = () => {
    setDisplayedDate(prev => {
      const year = prev.getFullYear();
      const month = prev.getMonth();
      return new Date(year, month - 1, 1);
    });
  };

  const handleNextMonth = () => {
    setDisplayedDate(prev => {
      const year = prev.getFullYear();
      const month = prev.getMonth();
      return new Date(year, month + 1, 1);
    });
  };

  const handleToday = () => {
    setDisplayedDate(new Date(today.getFullYear(), today.getMonth(), 1));
  };


  const renderCalendarHeader = () => {
    const monthYearLabel = displayedDate.toLocaleString('default', { month: 'long', year: 'numeric' });
    const btnOverrides = {
      primaryBackground: '#696969',
      primaryBorderColor: '#696969',
      primaryActiveBackground: '#696969',
      primaryHoverBackground: '#696969',
    }
    return (
      <Flex>
        <FlexItem shouldGrow shouldShrink>
          <Button padding="small" onClick={handleToday}>Today</Button>
          <View as="span" padding="small" className='nav-button-group'>
            <Button renderIcon={IconArrowStartLine} id="prev-month" onClick={handlePrevMonth}/>
            <Button renderIcon={IconArrowEndLine} id="next-month" onClick={handleNextMonth}/>
          </View>
          <View as="span" padding="small">
            <Text>
              {monthYearLabel}
            </Text>
          </View>
        </FlexItem>
        <FlexItem>
          <View as="span" padding="small" className='view-type-btn-group'>
            <Button
              color={viewType === 'week' ? 'primary' : 'secondary'}
              onClick={() => setViewType('week')}
              themeOverride={btnOverrides}
            >
              Week
            </Button>
            <Button
              color={viewType === 'month' ? 'primary' : 'secondary'}
              onClick={() => setViewType('month')}
              themeOverride={btnOverrides}
            >
              Month
            </Button>
            <Button
              color={viewType === 'agenda' ? 'primary' : 'secondary'}
              onClick={() => setViewType('agenda')}
              themeOverride={btnOverrides}
            >
              Agenda
            </Button>
          </View>
        </FlexItem>
      </Flex>
    );
  };

  return (
    <Flex gap="medium">
      <FlexItem size='75%' align="start">
        <View as="div" padding="small" id="cal-content-wrapper">
          <View as="div" id="cal-header-bar">
            {renderCalendarHeader()}
          </View>
          <View as="div" id="cal-left-side-content">
            {(() => {
              switch (viewType) {
                case 'month':
                  return <MonthView daysOfWeek={daysOfWeek} meta={meta} today={today} />;
                case 'week':
                  return <View>Temp Week View</View>;
                case 'agenda':
                  return <AgendaView />;
                default:
                  return null;
              }
            })()}
          </View>
        </View>
      </FlexItem>
      <FlexItem size='25%' align="start">
        <View as="div" padding="small" id="cal-right-side-wrapper">
          <Calendar
            visibleMonth={displayedDate.getFullYear() + '-' + String(displayedDate.getMonth() + 1).padStart(2, '0')}
            onDateSelected={(date) => {
              // date is a string in 'YYYY-MM-DD' format
              const [year, month, day] = date.split('-').map(Number);
              setDisplayedDate(new Date(year, month - 1, 1));
            }}
          />
        </View>
      </FlexItem>
    </Flex>
  );
};

export default CalendarLayout;
