import React from 'react';
import { Grid } from '@instructure/ui-grid';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';


const hours = [
  'all-day',
  '12am', '1am', '2am', '3am', '4am', '5am', '6am', '7am', '8am', '9am', '10am', '11am',
  '12pm', '1pm', '2pm', '3pm', '4pm', '5pm', '6pm', '7pm', '8pm', '9pm', '10pm', '11pm'
];

const WeekView = ({ daysOfWeek, cells, renderDayCell, weekIndex }) => {
  // weekIndex: which week to show (0-based)
  const startIdx = weekIndex * 7;
  const weekCells = cells.slice(startIdx, startIdx + 7);

  // Header row: empty cell for hours, then days of week
  return (
    <Grid colSpacing="none" rowSpacing="none">
      <Grid.Row>
        <Grid.Col key="hours-header" />
        {weekCells.map((cell, idx) => (
          <Grid.Col key={idx}>
            <View as="div" textAlign="center" padding="small">
              <Text size="large" weight="bold">{cell.day}</Text>
              <Text color="secondary">{daysOfWeek[idx]}</Text>
            </View>
          </Grid.Col>
        ))}
      </Grid.Row>
      {hours.map((hour, hourIdx) => (
        <Grid.Row key={hourIdx}>
          <Grid.Col key="hour-label">
            <View as="div" textAlign="end" padding="xx-small">
              <Text color="secondary">{hour}</Text>
            </View>
          </Grid.Col>
          {weekCells.map((cell, colIdx) => (
            <Grid.Col key={colIdx}>
              {/* Empty cell for now, can add events later */}
            </Grid.Col>
          ))}
        </Grid.Row>
      ))}
    </Grid>
  );
};

export default WeekView;
