# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Role < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  canvas_sync_features :defaults

  validates :canvas_id, uniqueness: true, presence: true
  has_many :admins, foreign_key: :canvas_role_id, primary_key: :canvas_id

  api_syncable({
                 canvas_id: :id,
                 label: :label,
                 base_role_type: :base_role_type,
                 canvas_account_id: ->(r) { r.dig('account', 'id') },
                 permissions: :permissions,
                 workflow_state: :workflow_state
               }, ->(api) { api.get("/api/v1/accounts/#{canvas_account_id}/roles/#{canvas_id}") })
end
