# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class User < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  include CrossShardUser

  canvas_sync_features :defaults

  # include CanvasSync::Concerns::LiveEventSync
  # around_process_live_event do |user, blk|
  #   blk.call
  # rescue Footrest::HttpError::Unauthorized => e
  #   # This can happen when a new user is created, but hasn't setup a login on Canvas yet.
  #   Rails.logger.info("Failed to fetch user #{canvas_user_id}: #{e.backtrace}")
  # end

  validates :canvas_id, uniqueness: true, presence: true
  has_many :pseudonyms, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :enrollments, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :courses, through: :enrollments
  has_many :admins, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :admin_roles, through: :admins, source: :role
  has_many :submissions, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :group_memberships, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :rubrics, primary_key: :canvas_id, foreign_key: :canvas_user_id

  has_many :active_course_admin_enrollments, lambda {
    where(workflow_state: ['active'], base_role_type: %w[TeacherEnrollment TaEnrollment DesignerEnrollment])
  }, primary_key: :canvas_id, foreign_key: :canvas_user_id, class_name: 'Enrollment'

  scope :active, -> { where(workflow_state: ['active']) }

  scope :active_students, lambda {
    active
      .joins(enrollments: :course)
      .where(enrollments: {
               workflow_state: ['active'],
               base_role_type: 'StudentEnrollment', courses: { workflow_state: ['active'] }
             })
      .distinct
  }

  scope :active_students_for_account, lambda { |canvas_account_id|
    active
      .joins(enrollments: :course)
      .where(enrollments: {
               workflow_state: ['active'], base_role_type: 'StudentEnrollment',
               courses: { workflow_state: ['active'], canvas_account_id: }
             })
      .distinct
  }

  scope :active_students_in_course_ids, lambda { |canvas_course_ids|
    active
      .joins(enrollments: :course)
      .where(enrollments: {
               workflow_state: %w[active concluded], base_role_type: 'StudentEnrollment',
               courses: { workflow_state: %w[active concluded], canvas_id: canvas_course_ids }
             })
      .distinct
  }

  def teacher?(course_id)
    enrollments.teacher.where(canvas_course_id: course_id).exists?
  end

  def ta?(course_id)
    enrollments.teacher.where(canvas_course_id: course_id).exists?
  end

  def designer?(course_id)
    enrollments.designer.where(canvas_course_id: course_id).exists?
  end

  api_syncable({
                 sis_id: :sis_user_id,
                 email: :email,
                 login_id: :login_id,
                 name: :name,
                 sortable_name: :sortable_name,
                 first_name: :short_name
               }, ->(api) { api.user_detail(canvas_id) })
end
