# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Term < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  canvas_sync_features :defaults

  validates :canvas_id, uniqueness: true, presence: true
  has_many :courses, foreign_key: :canvas_term_id, primary_key: :canvas_id
  belongs_to :grading_period_group, primary_key: :canvas_id, foreign_key: :grading_period_group_id, optional: true

  api_syncable({
                 canvas_id: :id,
                 name: :name,
                 sis_id: :sis_term_id,
                 start_at: :start_at,
                 end_at: :end_at,
                 workflow_state: :workflow_state,
                 grading_period_group_id: :grading_period_group_id
               }, lambda { |api|
                    terms = api.terms('self').all_pages!
                    term_data = terms.find { |term| term['id'] == canvas_id }
                    raise Footrest::HttpError::NotFound unless term_data.present?

                    term_data
                  })

  # This is a sample scope created by the CanvasSync gem; feel
  # free to customize it for your tool's requirements.
  scope :active, lambda {
    where(workflow_state: 'active')
      .where('start_at <= ? OR start_at IS NULL', 15.days.from_now)
      .where('end_at >= ? OR end_at IS NULL', 15.days.ago)
  }
end
