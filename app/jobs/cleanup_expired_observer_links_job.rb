# frozen_string_literal: true

class CleanupExpiredObserverLinksJob < ApplicationJob
  queue_as :default

  def perform
    Rails.logger.info 'Starting cleanup of expired observer links'
    expired_links = StudentObserverLink.needs_cleanup

    if expired_links.any?
      Rails.logger.info "Found #{expired_links.count} expired observer links to clean up"

      expired_links.update_all(status: 'expired')
    else
      Rails.logger.info 'No expired observer links found'
    end
  end
end
