# frozen_string_literal: true

class CanvasSyncStarterWorker < ApplicationJob
  queue_as :default

  # rubocop:disable Style/MutableConstant
  MODELS_TO_SYNC = %w[
    users
    pseudonyms
    courses
    accounts
    terms
    enrollments
    roles
    admins
    user_observers
  ]
  # rubocop:enable Style/MutableConstant

  def perform(_opts = {})
    api_acc = canvas_sync_client.account('self', includes: ['global_id'])
    current_organization.tap do |org|
      org.canvas_shard_id = (api_acc[:global_id] / PandaPal::Organization::SHARD_OFFSET).floor
      org.settings[:canvas][:default_time_zone] = api_acc[:default_time_zone]
      org.save!
    end

    job_chain = CanvasSync.default_provisioning_report_chain(
      MODELS_TO_SYNC,
      options: {},
      term_scope: 'active',
      updated_after: Rails.env.development? ? false : nil
    )
    job_chain.insert({ job: RefreshCrossShardUsersJob })

    job_chain.process!
  end
end
