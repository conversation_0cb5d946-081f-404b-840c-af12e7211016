# frozen_string_literal: true

class ApplicationController < ActionController::Base
  protect_from_forgery with: :exception
  prepend_before_action :forbid_access_if_lacking_session
  check_authorization

  around_action :set_time_zone

  rescue_from CanCan::AccessDenied do |_e|
    respond_to do |format|
      format.html { render component: 'UnAuthorized', prerender: false }
      format.json do
        render json: { message: 'You are not authorized.' }, status: 401
      end
    end
  end

  rescue_from ActiveRecord::RecordNotFound do |e|
    respond_to do |format|
      format.json do
        render json: { error: e.message }, status: :not_found
      end
    end
  end

  def js_env(opts = {})
    if @js_env.nil?
      @js_env = {}
      @js_env[:params] = params.except('action', 'controller')
      @js_env[:csrf_token] = form_authenticity_token
      @js_env[:session_key] = current_session.session_key
      @js_env[:organization_id] = current_organization.id
      @js_env[:base_path] = url_for(path: nil, only_path: true)
      @js_env[:canvas_url] = current_organization.canvas_url
      @js_env[:environment] = Rails.env
      @js_env[:timezone] = current_organization.settings[:canvas][:default_time_zone] || 'UTC'
      @js_env[:current_user_id] = current_user&.canvas_id
      @js_env[:current_account] = current_account
      @js_env[:current_time_zone] = current_time_zone
      @js_env[:abilities] = {
        is_account_context: current_ability.launch_context.is_a?(Account),
        is_course_context: current_ability.launch_context.is_a?(Course)
      }

      if panda_session.present?
        @js_env[:session_key] = current_session.session_key

        if current_session_data[:launch_params].present?
          @js_env[:post_message_token] = current_session_data[:launch_params][:custom_canvas_post_message_token]
          @js_env[:brand_variables] = parse_brand_config
          @js_env[:launch_params] = current_session_data[:launch_params]
        end
      end
    end
    @js_env.deep_merge! opts
    @js_env
  end
  helper_method :js_env

  def current_account
    @current_account ||= Account.find_or_initialize_by(canvas_id: current_canvas_account_id).tap do |account|
      account.sync_from_api if account.new_record?
    end
  end

  def current_user
    @current_user ||= User.find_or_initialize_by(canvas_id: current_canvas_user_id).tap do |user|
      user.sync_from_api if user.new_record?
    end
  end

  private

  def current_canvas_account_id
    @current_canvas_account_id ||= current_session.get_lti_cust_param('canvas_account_id')
  end

  def current_canvas_user_id
    @current_canvas_user_id ||= current_session.get_lti_cust_param('custom_canvas_user_id')
  end

  def current_ability
    @current_ability ||= Ability.new(current_user, rails_session: session, panda_session:)
  end

  def panda_session
    current_session(create_missing: false)
  end

  def parse_brand_config
    JSON.parse(current_session_data[:launch_params][:custom_canvas_brand_config] || '{}')
  rescue StandardError
    {}
  end

  def pagination_params
    params.permit(:per_page, :page)
  end

  def current_time_zone
    @current_time_zone ||= current_session&.custom_lti_params&.dig(:canvas_user_timezone) || 'UTC'
  end

  def set_time_zone(&)
    Time.use_zone(current_time_zone, &)
  end
end
