# frozen_string_literal: true

class Api::V1::Students::CalendarController < ApplicationController
  before_action :set_student

  def events
    authorize! :read, User

    service = Calendar::EventsService.new(@student.canvas_id, calendar_params)
    @events = service.call
  end

  def planner_notes
    authorize! :read, User

    service = Calendar::PlannerNotesService.new(@student.canvas_id, calendar_params)
    @planner_notes = service.call
  end

  def items
    authorize! :read, User

    service = Calendar::ItemsService.new(@student.canvas_id, calendar_params)
    @items = service.call
  end

  private

  def set_student
    @student = find_student_by_canvas_id(params[:student_id])

    unless @student
      render json: { error: 'Student not found' }, status: :not_found
      return
    end
  end

  def find_student_by_canvas_id(canvas_id)
    launch_context_id = current_ability.launch_context.canvas_id
    base_query = if current_ability.launch_context.is_a?(Account)
                   User.active_students_for_account(launch_context_id)
                 elsif current_ability.launch_context.is_a?(Course)
                   User.active_students_in_course_ids([launch_context_id])
                 else
                   User.none
                 end

    base_query.find_by(canvas_id: canvas_id)
  end

  def calendar_params
    params.permit(
      :start_date, :end_date, :type, :undated, :filter, include: [], context_codes: []
    ).to_h.symbolize_keys
  end
end
