# frozen_string_literal: true

class LtiController < ApplicationController
  before_action :set_js_env

  def account_navigation
    authorize! :launch_from, :account
    render component: 'AdminDashboard', prerender: false
  end

  def course_navigation
    authorize! :launch_from, :course
    render component: 'CourseDashboard', prerender: false
  end

  private

  def set_js_env
    js_env({
             launch_point: action_name
           })
  end
end
