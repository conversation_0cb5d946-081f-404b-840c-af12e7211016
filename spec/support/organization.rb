# frozen_string_literal: true

RSpec.configure do |config|
  config.before(:suite) do
    begin
      Apartment::Tenant.drop('test')
    rescue StandardError
      nil
    end
    PandaPal::Organization.find_by(name: 'test')&.delete
    FactoryBot.create(:organization)
  end

  config.before(:each) do |_example|
    switch_tenant('test')
  end

  config.after(:each) do |_example|
    Apartment::Tenant.reset
  end
end
