# frozen_string_literal: true

FactoryBot.define do
  factory :session, class: 'PandaPal::Session' do
    session_key { SecureRandom.hex(16) }

    panda_pal_organization_id { PandaPal::Organization.last.id }

    data do
      { canvas_user_id: user.canvas_id, user_id: user.id, launch_params: {
        'https://purl.imsglobal.org/spec/lti/claim/custom' => { canvas_user_id: user.canvas_id }
      } }
    end

    transient do
      user { User.first || FactoryBot.create(:user) }
      account { Account.first || FactoryBot.create(:account) }
      launch_context { account }
    end

    trait :admin do
      after :build do |session, evaluator|
        session.data[:launch_params]['https://purl.imsglobal.org/spec/lti/claim/roles'] = [
          'http://purl.imsglobal.org/vocab/lis/v2/institution/person#Administrator',
          'http://purl.imsglobal.org/vocab/lis/v2/system/person#SysAdmin'
        ]
        session.data[:launch_params]['https://purl.imsglobal.org/spec/lti/claim/custom'] = {
          canvas_user_id: evaluator.user.canvas_id,
          canvas_account_id: evaluator.account.canvas_id,
          canvas_course_id: evaluator.launch_context.is_a?(Course) ? evaluator.launch_context.canvas_id : nil
        }
      end
    end

    trait :teacher do
      after :build do |session, evaluator|
        session.data[:launch_params]['https://purl.imsglobal.org/spec/lti/claim/roles'] = [
          'http://purl.imsglobal.org/vocab/lis/v2/institution/person#Teacher'
        ]

        if evaluator
          session.data[:launch_params]['https://purl.imsglobal.org/spec/lti/claim/custom'] = {
            canvas_user_id: evaluator.user.canvas_id,
            canvas_account_id: evaluator.account.canvas_id,
            canvas_course_id: evaluator.launch_context.is_a?(Course) ? evaluator.launch_context.canvas_id : nil
          }
        end
      end
    end
  end
end
