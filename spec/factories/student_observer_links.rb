# frozen_string_literal: true

FactoryBot.define do
  factory :student_observer_link do
    association :observer_user, factory: :user
    association :observed_student, factory: :user

    expires_at { 1.hour.from_now }
    status { 'active' }

    trait :expired do
      expires_at { 1.hour.ago }
      status { 'expired' }
    end

    trait :ended do
      status { 'ended' }
    end

    trait :renewed do
      renewed_at { 30.minutes.ago }
      expires_at { 90.minutes.ago }
    end

    trait :about_to_expire do
      expires_at { 5.minutes.from_now }
    end
  end
end
