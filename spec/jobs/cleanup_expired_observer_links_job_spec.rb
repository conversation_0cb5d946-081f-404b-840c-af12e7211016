# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CleanupExpiredObserverLinksJob, type: :job do
  describe '#perform' do
    let(:observer) { create(:user) }
    let(:student1) { create(:user) }
    let(:student2) { create(:user) }
    let(:student3) { create(:user) }

    context 'when there are expired observer links to clean up' do
      let!(:expired_link1) do
        create(:student_observer_link,
               observer_user: observer,
               observed_student: student1,
               expires_at: 2.hours.ago,
               status: 'active')
      end

      let!(:expired_link2) do
        create(:student_observer_link,
               observer_user: create(:user),
               observed_student: student2,
               expires_at: 1.hour.ago,
               status: 'active')
      end

      let!(:active_link) do
        create(:student_observer_link,
               observer_user: create(:user),
               observed_student: student3,
               expires_at: 1.hour.from_now,
               status: 'active')
      end

      let!(:already_expired_link) do
        create(:student_observer_link,
               observer_user: create(:user),
               observed_student: create(:user),
               expires_at: 3.hours.ago,
               status: 'expired')
      end

      it 'marks expired active links as expired' do
        expect(Rails.logger).to receive(:info).with('Starting cleanup of expired observer links')
        expect(Rails.logger).to receive(:info).with('Found 2 expired observer links to clean up')

        described_class.new.perform

        expired_link1.reload
        expired_link2.reload
        active_link.reload
        already_expired_link.reload

        expect(expired_link1.status).to eq('expired')
        expect(expired_link2.status).to eq('expired')
        expect(active_link.status).to eq('active')
        expect(already_expired_link.status).to eq('expired')
      end

      it 'only processes links that need cleanup' do
        expect(StudentObserverLink.needs_cleanup.count).to eq(2)

        described_class.new.perform

        # Verify that only the expired active links were updated
        expect(StudentObserverLink.where(status: 'expired').count).to eq(3) # 2 newly expired + 1 already expired
        expect(StudentObserverLink.where(status: 'active').count).to eq(1) # 1 still active
      end

      it 'logs the correct number of expired links found' do
        allow(Rails.logger).to receive(:info)

        described_class.new.perform

        expect(Rails.logger).to have_received(:info).with('Starting cleanup of expired observer links')
        expect(Rails.logger).to have_received(:info).with('Found 2 expired observer links to clean up')
      end
    end

    context 'when there are no expired observer links to clean up' do
      let!(:active_link1) do
        create(:student_observer_link,
               observer_user: observer,
               observed_student: student1,
               expires_at: 1.hour.from_now,
               status: 'active')
      end

      let!(:active_link2) do
        create(:student_observer_link,
               observer_user: create(:user),
               observed_student: student2,
               expires_at: 2.hours.from_now,
               status: 'active')
      end

      let!(:already_expired_link) do
        create(:student_observer_link,
               observer_user: create(:user),
               observed_student: student3,
               expires_at: 1.hour.ago,
               status: 'expired')
      end

      it 'does not update any links' do
        expect(Rails.logger).to receive(:info).with('Starting cleanup of expired observer links')
        expect(Rails.logger).to receive(:info).with('No expired observer links found')

        expect do
          described_class.new.perform
        end.not_to(change { StudentObserverLink.where(status: 'expired').count })

        active_link1.reload
        active_link2.reload
        already_expired_link.reload

        expect(active_link1.status).to eq('active')
        expect(active_link2.status).to eq('active')
        expect(already_expired_link.status).to eq('expired')
      end

      it 'logs that no expired links were found' do
        allow(Rails.logger).to receive(:info)

        described_class.new.perform

        expect(Rails.logger).to have_received(:info).with('Starting cleanup of expired observer links')
        expect(Rails.logger).to have_received(:info).with('No expired observer links found')
      end
    end

    context 'when there are no observer links at all' do
      it 'logs that no expired links were found' do
        expect(Rails.logger).to receive(:info).with('Starting cleanup of expired observer links')
        expect(Rails.logger).to receive(:info).with('No expired observer links found')

        described_class.new.perform

        expect(StudentObserverLink.count).to eq(0)
      end
    end

    context 'job queue configuration' do
      it 'is queued on the default queue' do
        expect(described_class.queue_name).to eq('default')
      end
    end

    context 'integration with needs_cleanup scope' do
      let!(:needs_cleanup_link1) do
        create(:student_observer_link,
               observer_user: observer,
               observed_student: student1,
               expires_at: 1.hour.ago,
               status: 'active')
      end

      let!(:needs_cleanup_link2) do
        create(:student_observer_link,
               observer_user: create(:user),
               observed_student: student2,
               expires_at: 30.minutes.ago,
               status: 'active')
      end

      let!(:does_not_need_cleanup) do
        create(:student_observer_link,
               observer_user: create(:user),
               observed_student: student3,
               expires_at: 30.minutes.from_now,
               status: 'active')
      end

      it 'uses the needs_cleanup scope correctly' do
        expect(StudentObserverLink.needs_cleanup).to include(needs_cleanup_link1, needs_cleanup_link2)
        expect(StudentObserverLink.needs_cleanup).not_to include(does_not_need_cleanup)

        described_class.new.perform

        needs_cleanup_link1.reload
        needs_cleanup_link2.reload
        does_not_need_cleanup.reload

        expect(needs_cleanup_link1.status).to eq('expired')
        expect(needs_cleanup_link2.status).to eq('expired')
        expect(does_not_need_cleanup.status).to eq('active')
      end
    end

    context 'performance considerations' do
      it 'uses update_all for efficiency' do
        create_list(:student_observer_link, 5,
                    expires_at: 1.hour.ago,
                    status: 'active')

        expect(StudentObserverLink).to receive(:needs_cleanup).and_call_original
        expect_any_instance_of(ActiveRecord::Relation).to receive(:update_all).with(status: 'expired').and_call_original

        described_class.new.perform
      end
    end
  end
end
