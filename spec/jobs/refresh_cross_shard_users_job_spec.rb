# frozen_string_literal: true

require 'rails_helper'

RSpec.describe RefreshCrossShardUsersJob, type: :job do
  describe '#perform' do
    around do |example|
      orgs = [
        create(:organization, name: 'A', canvas_shard_id: 1),
        create(:organization, name: 'B', canvas_shard_id: 2)
      ]
      example.call
      orgs.each(&:destroy)
    end

    let!(:org1) { PandaPal::Organization.find_by(name: 'A') }
    let!(:org2) { PandaPal::Organization.find_by(name: 'B') }

    before do
      org1.switch_tenant
      create(:user, canvas_id: 1)

      org2.switch_tenant
      create(:user, canvas_id: 1 + PandaPal::Organization::SHARD_OFFSET)
    end

    it 'adds the local shard to each User in the origin shard' do
      org1.switch_tenant
      expect(UserShardAssociation.count).to eq(0)

      org2.switch_tenant
      RefreshCrossShardUsersJob.perform_now

      org1.switch_tenant
      expect(UserShardAssociation.count).to eq(1)
      expect(UserShardAssociation.first.canvas_shard_id).to eq(2)
    end
  end
end
