# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::ObserverLinksController', type: :request do
  let(:organization) { current_organization }
  let!(:account) { create(:account) }
  let!(:course) { create(:course, account: account) }

  let!(:account_admin) { create(:admin, account: account).user }
  let!(:course_admin) { create(:enrollment, :teacher, user: create(:user), course: course).user }

  let!(:admin_session) { create(:session, :admin, user: account_admin, account: account, launch_context: account) }
  let!(:course_admin_session) { create(:session, :teacher, user: course_admin, account: account, launch_context: course) }

  let!(:student) { create(:user) }
  let!(:student_enrollment) { create(:enrollment, :student, user: student, course: course) }

  shared_context 'with account admin session' do
    let(:current_session) { admin_session }
    let(:current_user) { account_admin }
    let(:default_params) { { session_key: admin_session.session_key } }
  end

  shared_context 'with course admin session' do
    let(:current_session) { course_admin_session }
    let(:current_user) { course_admin }
    let(:default_params) { { session_key: course_admin_session.session_key } }
  end

  shared_context 'with active observer link' do
    let!(:observer_link) do
      create(:student_observer_link,
             observer_user: current_user,
             observed_student: student)
    end
  end

  shared_context 'with expired observer link' do
    let!(:observer_link) do
      create(:student_observer_link, :expired,
             observer_user: current_user,
             observed_student: student)
    end
  end

  shared_context 'with ended observer link' do
    let!(:observer_link) do
      create(:student_observer_link, :ended,
             observer_user: current_user,
             observed_student: student)
    end
  end

  shared_context 'with renewed observer link' do
    let!(:observer_link) do
      create(:student_observer_link, :renewed,
             observer_user: current_user,
             observed_student: student)
    end
  end

  describe 'GET /api/v1/observer_link/current' do
    shared_examples 'current observer link functionality' do
      context 'when user has an active observer link' do
        include_context 'with active observer link'

        it 'returns the current active observer link' do
          get api_v1_observer_link_current_path(organization_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:data]).to include(
            :organization_id,
            :id,
            :observer_user_id,
            :observed_student,
            :created_at,
            :expires_at,
            :time_remaining_minutes,
            :can_be_renewed,
            :status
          )
          expect(json_response[:data][:observer_user_id]).to eq(current_user.canvas_id)
          expect(json_response[:data][:observed_student][:canvas_id]).to eq(student.canvas_id)
          expect(json_response[:data][:status]).to eq('active')
        end
      end

      context 'when user has no active observer link' do
        it 'returns null data' do
          get api_v1_observer_link_current_path(organization_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:data]).to be_nil
        end
      end

      context 'when user has an expired observer link' do
        include_context 'with expired observer link'

        it 'returns null data' do
          get api_v1_observer_link_current_path(organization_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:data]).to be_nil
        end
      end

      context 'when user has an ended observer link' do
        include_context 'with ended observer link'

        it 'returns null data' do
          get api_v1_observer_link_current_path(organization_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:data]).to be_nil
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'current observer link functionality'
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'current observer link functionality'
    end
  end

  describe 'PATCH /api/v1/observer_link/renew/:id' do
    shared_examples 'observer link renewal functionality' do
      context 'when observer link can be renewed' do
        include_context 'with active observer link'

        it 'renews the observer link successfully' do
          original_expires_at = observer_link.expires_at

          patch api_v1_observer_link_renew_path(organization_id: organization.id, id: observer_link.id, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:message]).to eq('Observer link renewed successfully')
          expect(json_response[:data]).to include(
            :organization_id,
            :id,
            :observer_user_id,
            :observed_student,
            :expires_at,
            :renewed_at,
            :time_remaining_minutes,
            :can_be_renewed,
            :status
          )

          observer_link.reload
          expect(observer_link.expires_at).to be > original_expires_at
          expect(observer_link.renewed_at).to be_present
          expect(observer_link.can_be_renewed?).to be false
        end
      end

      context 'when observer link cannot be renewed' do
        include_context 'with renewed observer link'

        it 'returns not found when link was already renewed (not active)' do
          patch api_v1_observer_link_renew_path(organization_id: organization.id, id: observer_link.id, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:not_found)
        end
      end

      context 'when observer link does not exist' do
        it 'returns not found error' do
          patch api_v1_observer_link_renew_path(organization_id: organization.id, id: 99_999, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:not_found)
        end
      end

      context 'when observer link is expired' do
        include_context 'with expired observer link'

        it 'returns not found when trying to renew expired link (not active)' do
          patch api_v1_observer_link_renew_path(organization_id: organization.id, id: observer_link.id, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:not_found)
        end
      end

      context 'when observer link is ended' do
        include_context 'with ended observer link'

        it 'returns not found when trying to renew ended link (not active)' do
          patch api_v1_observer_link_renew_path(organization_id: organization.id, id: observer_link.id, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'observer link renewal functionality'
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'observer link renewal functionality'
    end

    context 'parameter validation' do
      include_context 'with account admin session'
      include_context 'with active observer link'

      it 'returns bad request when org_id is missing' do
        patch api_v1_observer_link_renew_path(organization_id: organization.id, id: observer_link.id), params: default_params

        expect(response).to have_http_status(:bad_request)
        expect(json_response[:error]).to eq('Organization ID is required')
      end

      it 'returns routing error when id is missing' do
        expect do
          patch api_v1_observer_link_renew_path(organization_id: organization.id, id: '', org_id: organization.id), params: default_params
        end.to raise_error(ActionController::RoutingError)
      end
    end
  end

  describe 'DELETE /api/v1/observer_link/end/:id' do
    shared_examples 'observer link ending functionality' do
      context 'when observer link exists and is active' do
        include_context 'with active observer link'

        it 'ends the observer link successfully' do
          delete api_v1_observer_link_end_path(organization_id: organization.id, id: observer_link.id, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:ok)
          expect(json_response[:message]).to eq('Observer link ended successfully')

          observer_link.reload
          expect(observer_link.status).to eq('ended')
        end
      end

      context 'when observer link is already ended' do
        include_context 'with ended observer link'

        it 'returns not found (link is not active)' do
          delete api_v1_observer_link_end_path(organization_id: organization.id, id: observer_link.id, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:not_found)
        end
      end

      context 'when observer link is expired' do
        include_context 'with expired observer link'

        it 'returns not found (link is not active)' do
          delete api_v1_observer_link_end_path(organization_id: organization.id, id: observer_link.id, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:not_found)
        end
      end

      context 'when observer link does not exist' do
        it 'returns not found error' do
          delete api_v1_observer_link_end_path(organization_id: organization.id, id: 99_999, org_id: organization.id), params: default_params

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context 'when authenticated as account admin' do
      include_context 'with account admin session'
      include_examples 'observer link ending functionality'
    end

    context 'when authenticated as course admin' do
      include_context 'with course admin session'
      include_examples 'observer link ending functionality'
    end

    context 'parameter validation' do
      include_context 'with account admin session'
      include_context 'with active observer link'

      it 'returns bad request when org_id is missing' do
        delete api_v1_observer_link_end_path(organization_id: organization.id, id: observer_link.id), params: default_params

        expect(response).to have_http_status(:bad_request)
        expect(json_response[:error]).to eq('Organization ID is required')
      end

      it 'returns not found when id is invalid' do
        delete api_v1_observer_link_end_path(organization_id: organization.id, id: 'invalid', org_id: organization.id), params: default_params

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
