# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Calendar::EventsService do
  let(:user_id) { 4 }
  let(:base_url) { 'http://canvas.docker' }
  let(:mock_calendar_events) do
    [
      {
        'id' => '5',
        'title' => 'TEST 1',
        'start_at' => '2025-09-03T19:15:00Z',
        'end_at' => '2025-09-03T22:30:00Z',
        'workflow_state' => 'active',
        'all_day' => false,
        'type' => 'event',
        'context_code' => 'user_4',
        'context_name' => 'Student 1',
        'url' => 'http://canvas.docker/api/v1/calendar_events/5'
      }
    ]
  end

  before do
    # Stub the canvas_sync_client to return a client with the test base URL
    allow(Kernel).to receive(:canvas_sync_client).and_return(
      Bearcat::Client.new(prefix: base_url, token: 'dummy_token')
    )
  end

  describe '#call' do
    context 'with basic parameters' do
      let(:service) { described_class.new(user_id) }

      it 'calls the Canvas API with correct path and basic parameters' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/calendar_events")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'start_date' => '2025-09-01T00:00:00.000Z',
              'end_date' => '2025-09-30T00:00:00.000Z'
            )
          )
          .to_return(
            status: 200,
            body: mock_calendar_events.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_calendar_events)
      end
    end

    context 'with date range options' do
      let(:options) do
        {
          start_date: '2025-08-31T18:30:00.000Z',
          end_date: '2025-10-05T18:30:00.000Z'
        }
      end
      let(:service) { described_class.new(user_id, options) }

      it 'includes date parameters in the API call' do
        stub_request(:get, "#{base_url}/api/v1/calendar_events")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'start_date' => '2025-08-31T18:30:00.000Z',
              'end_date' => '2025-10-05T18:30:00.000Z'
            )
          )
          .to_return(
            status: 200,
            body: mock_calendar_events.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_calendar_events)
      end
    end

    context 'with context_codes option' do
      let(:options) { { context_codes: %w[user_4 course_2 course_1] } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes context codes in the API call' do
        stub_request(:get, "#{base_url}/api/v1/calendar_events")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'context_codes[]' => %w[user_4 course_2 course_1]
            )
          )
          .to_return(
            status: 200,
            body: mock_calendar_events.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_calendar_events)
      end
    end

    context 'with include parameters' do
      let(:options) { { include: %w[web_conference series_head series_natural_language] } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes include parameters in the API call' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/calendar_events")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'include[]' => %w[web_conference series_head series_natural_language]
            )
          )
          .to_return(
            status: 200,
            body: mock_calendar_events.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_calendar_events)
      end
    end

    context 'with type parameter' do
      let(:options) { { type: 'assignment' } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes type parameter in the API call' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/calendar_events")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'type' => 'assignment'
            )
          )
          .to_return(
            status: 200,
            body: mock_calendar_events.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_calendar_events)
      end
    end

    context 'with undated parameter' do
      let(:options) { { undated: true } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes undated parameter in the API call' do
        stub_request(:get, "#{base_url}/api/v1/calendar_events")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'undated' => '1'
            )
          )
          .to_return(
            status: 200,
            body: mock_calendar_events.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_calendar_events)
      end
    end
  end
end

    context 'with include options' do
      let(:options) { { include: ['web_conference', 'series_head'] } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes include parameters in the API call' do
        expect(mock_client).to receive(:get).with(
          'api/v1/calendar_events',
          hash_including(
            include: ['web_conference', 'series_head']
          )
        )

        service.call
      end
    end

    context 'with type filter' do
      let(:options) { { type: 'assignment' } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes type parameter in the API call' do
        expect(mock_client).to receive(:get).with(
          'api/v1/calendar_events',
          hash_including(
            type: 'assignment'
          )
        )

        service.call
      end
    end

    context 'with pagination options' do
      let(:options) { { per_page: 100, page: 2 } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes pagination parameters in the API call' do
        expect(mock_client).to receive(:get).with(
          'api/v1/calendar_events',
          hash_including(
            per_page: 100,
            page: 2
          )
        )

        service.call
      end
    end

    it 'calls all_pages! on the response' do
      service = described_class.new(user_id)
      expect(mock_response).to receive(:all_pages!)

      service.call
    end
  end
end
