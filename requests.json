{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [{"startedDateTime": "2025-09-04T07:23:16.053Z", "id": "page_1", "title": "http://canvas.docker/calendar", "pageTimings": {"onContentLoad": 1339.8619999934454, "onLoad": 2349.9120000051335}}], "entries": [{"_connectionId": "1871272", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "n", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12911}, {"functionName": "fetch", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 13984}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "26378", "url": "webpack-internal:///./ui/shared/do-fetch-api-effect/index.ts", "lineNumber": 107, "columnNumber": 24}, {"functionName": "step", "scriptId": "25324", "url": "webpack-internal:///./node_modules/tslib/tslib.es6.mjs", "lineNumber": 182, "columnNumber": 20}, {"functionName": "eval", "scriptId": "25324", "url": "webpack-internal:///./node_modules/tslib/tslib.es6.mjs", "lineNumber": 163, "columnNumber": 50}, {"functionName": "asyncGeneratorStep", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 6, "columnNumber": 27}, {"functionName": "_next", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 23, "columnNumber": 16}, {"functionName": "eval", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 30, "columnNumber": 12}, {"functionName": "eval", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 19, "columnNumber": 15}, {"functionName": "doFetchApi", "scriptId": "26378", "url": "webpack-internal:///./ui/shared/do-fetch-api-effect/index.ts", "lineNumber": 152, "columnNumber": 7}, {"functionName": "eval", "scriptId": "26973", "url": "webpack-internal:///./ui/features/navigation_header/react/queries/unreadCountQuery.ts", "lineNumber": 51, "columnNumber": 95}, {"functionName": "step", "scriptId": "25324", "url": "webpack-internal:///./node_modules/tslib/tslib.es6.mjs", "lineNumber": 182, "columnNumber": 20}, {"functionName": "eval", "scriptId": "25324", "url": "webpack-internal:///./node_modules/tslib/tslib.es6.mjs", "lineNumber": 163, "columnNumber": 50}, {"functionName": "asyncGeneratorStep", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 6, "columnNumber": 27}, {"functionName": "_next", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 23, "columnNumber": 16}, {"functionName": "eval", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 30, "columnNumber": 12}, {"functionName": "eval", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 19, "columnNumber": 15}, {"functionName": "getUnreadCount", "scriptId": "26973", "url": "webpack-internal:///./ui/features/navigation_header/react/queries/unreadCountQuery.ts", "lineNumber": 70, "columnNumber": 7}, {"functionName": "fetchFn", "scriptId": "26539", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/query.js", "lineNumber": 194, "columnNumber": 13}, {"functionName": "run", "scriptId": "26540", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/retryer.js", "lineNumber": 90, "columnNumber": 48}, {"functionName": "start", "scriptId": "26540", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/retryer.js", "lineNumber": 131, "columnNumber": 8}, {"functionName": "fetch", "scriptId": "26539", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/query.js", "lineNumber": 276, "columnNumber": 25}, {"functionName": "#executeFetch", "scriptId": "26534", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/queryObserver.js", "lineNumber": 179, "columnNumber": 37}, {"functionName": "onSubscribe", "scriptId": "26534", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/queryObserver.js", "lineNumber": 56, "columnNumber": 26}, {"functionName": "subscribe", "scriptId": "26536", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/subscribable.js", "lineNumber": 12, "columnNumber": 9}, {"functionName": "eval", "scriptId": "26544", "url": "webpack-internal:///./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js", "lineNumber": 62, "columnNumber": 55}, {"functionName": "subscribeToStore", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 16138, "columnNumber": 9}, {"functionName": "commitHookEffectListMount", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 23183, "columnNumber": 25}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 24959, "columnNumber": 12}, {"functionName": "commitPassiveMountEffects_complete", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 24924, "columnNumber": 8}, {"functionName": "commitPassiveMountEffects_begin", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 24911, "columnNumber": 6}, {"functionName": "commitPassiveMountEffects", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 24899, "columnNumber": 2}, {"functionName": "flushPassiveEffectsImpl", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 27072, "columnNumber": 2}, {"functionName": "flushPassiveEffects", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 27017, "columnNumber": 13}, {"functionName": "performConcurrentWorkOnRoot", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 25742, "columnNumber": 31}, {"functionName": "workLoop", "scriptId": "25524", "url": "webpack-internal:///./node_modules/scheduler/cjs/scheduler.development.js", "lineNumber": 265, "columnNumber": 33}, {"functionName": "flushWork", "scriptId": "25524", "url": "webpack-internal:///./node_modules/scheduler/cjs/scheduler.development.js", "lineNumber": 238, "columnNumber": 13}, {"functionName": "performWorkUntilDeadline", "scriptId": "25524", "url": "webpack-internal:///./node_modules/scheduler/cjs/scheduler.development.js", "lineNumber": 532, "columnNumber": 20}], "parentId": {"id": "556", "debuggerId": "-4999200538019608037.702731756911841636"}}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/release_notes/unread_count", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "accept", "value": "application/json+canvas-string-ids, application/json"}, {"name": "x-csrf-token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "x-requested-with", "value": "XMLHttpRequest"}], "queryString": [], "cookies": [], "headersSize": 1214, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"f5948b4e1f3bb7057157067b074c203f\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=16.38, cache_fetch_hit.active_support;dur=0.11, cache_read_multi.active_support;dur=0.36, start_processing.action_controller;dur=0.01, sql.active_record;dur=1.77, instance.active_record;dur=0.00, instantiation.active_record;dur=0.63, process_action.action_controller;dur=24.28"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=release_notes;n=unread_count;b=1803664;m=1803664;u=0.01;y=0.01;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "600.0"}, {"name": "x-request-context-id", "value": "6c885ba7-fda1-4e9e-b9bd-598f23fa919e"}, {"name": "x-request-cost", "value": "0.011821415995243001"}, {"name": "x-runtime", "value": "0.440898"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 18, "mimeType": "application/json", "compression": -11, "text": "{\"unread_count\":0}"}, "redirectURL": "", "headersSize": 1524, "bodySize": 29, "_transferSize": 1553, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.344Z", "time": 450.27099997969344, "timings": {"blocked": 1.9020000020079315, "dns": -1, "ssl": -1, "connect": -1, "send": 0.050999999999999934, "wait": 447.6099999999367, "receive": 0.707999977748841, "_blocked_queueing": 1.2310000020079315, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871548", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "n", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12911}, {"functionName": "fetch", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 13984}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "26378", "url": "webpack-internal:///./ui/shared/do-fetch-api-effect/index.ts", "lineNumber": 107, "columnNumber": 24}, {"functionName": "step", "scriptId": "25324", "url": "webpack-internal:///./node_modules/tslib/tslib.es6.mjs", "lineNumber": 182, "columnNumber": 20}, {"functionName": "eval", "scriptId": "25324", "url": "webpack-internal:///./node_modules/tslib/tslib.es6.mjs", "lineNumber": 163, "columnNumber": 50}, {"functionName": "asyncGeneratorStep", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 6, "columnNumber": 27}, {"functionName": "_next", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 23, "columnNumber": 16}, {"functionName": "eval", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 30, "columnNumber": 12}, {"functionName": "eval", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 19, "columnNumber": 15}, {"functionName": "doFetchApi", "scriptId": "26378", "url": "webpack-internal:///./ui/shared/do-fetch-api-effect/index.ts", "lineNumber": 152, "columnNumber": 7}, {"functionName": "eval", "scriptId": "26973", "url": "webpack-internal:///./ui/features/navigation_header/react/queries/unreadCountQuery.ts", "lineNumber": 51, "columnNumber": 95}, {"functionName": "step", "scriptId": "25324", "url": "webpack-internal:///./node_modules/tslib/tslib.es6.mjs", "lineNumber": 182, "columnNumber": 20}, {"functionName": "eval", "scriptId": "25324", "url": "webpack-internal:///./node_modules/tslib/tslib.es6.mjs", "lineNumber": 163, "columnNumber": 50}, {"functionName": "asyncGeneratorStep", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 6, "columnNumber": 27}, {"functionName": "_next", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 23, "columnNumber": 16}, {"functionName": "eval", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 30, "columnNumber": 12}, {"functionName": "eval", "scriptId": "25323", "url": "webpack-internal:///./node_modules/@swc/helpers/esm/_async_to_generator.js", "lineNumber": 19, "columnNumber": 15}, {"functionName": "getUnreadCount", "scriptId": "26973", "url": "webpack-internal:///./ui/features/navigation_header/react/queries/unreadCountQuery.ts", "lineNumber": 70, "columnNumber": 7}, {"functionName": "persisterFn", "scriptId": "26560", "url": "webpack-internal:///./node_modules/@tanstack/query-persist-client-core/build/modern/createPersister.js", "lineNumber": 58, "columnNumber": 32}], "parent": {"description": "await", "callFrames": [{"functionName": "fetchFn", "scriptId": "26539", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/query.js", "lineNumber": 188, "columnNumber": 28}, {"functionName": "run", "scriptId": "26540", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/retryer.js", "lineNumber": 90, "columnNumber": 48}, {"functionName": "start", "scriptId": "26540", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/retryer.js", "lineNumber": 131, "columnNumber": 8}, {"functionName": "fetch", "scriptId": "26539", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/query.js", "lineNumber": 276, "columnNumber": 25}, {"functionName": "#executeFetch", "scriptId": "26534", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/queryObserver.js", "lineNumber": 179, "columnNumber": 37}, {"functionName": "onSubscribe", "scriptId": "26534", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/queryObserver.js", "lineNumber": 56, "columnNumber": 26}, {"functionName": "subscribe", "scriptId": "26536", "url": "webpack-internal:///./node_modules/@tanstack/query-core/build/modern/subscribable.js", "lineNumber": 12, "columnNumber": 9}, {"functionName": "eval", "scriptId": "26544", "url": "webpack-internal:///./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js", "lineNumber": 62, "columnNumber": 55}, {"functionName": "subscribeToStore", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 16138, "columnNumber": 9}, {"functionName": "commitHookEffectListMount", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 23183, "columnNumber": 25}, {"functionName": "commitPassiveMountOnFiber", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 24959, "columnNumber": 12}, {"functionName": "commitPassiveMountEffects_complete", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 24924, "columnNumber": 8}, {"functionName": "commitPassiveMountEffects_begin", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 24911, "columnNumber": 6}, {"functionName": "commitPassiveMountEffects", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 24899, "columnNumber": 2}, {"functionName": "flushPassiveEffectsImpl", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 27072, "columnNumber": 2}, {"functionName": "flushPassiveEffects", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 27017, "columnNumber": 13}, {"functionName": "performConcurrentWorkOnRoot", "scriptId": "25522", "url": "webpack-internal:///./node_modules/react-dom/cjs/react-dom.development.js", "lineNumber": 25742, "columnNumber": 31}, {"functionName": "workLoop", "scriptId": "25524", "url": "webpack-internal:///./node_modules/scheduler/cjs/scheduler.development.js", "lineNumber": 265, "columnNumber": 33}, {"functionName": "flushWork", "scriptId": "25524", "url": "webpack-internal:///./node_modules/scheduler/cjs/scheduler.development.js", "lineNumber": 238, "columnNumber": 13}, {"functionName": "performWorkUntilDeadline", "scriptId": "25524", "url": "webpack-internal:///./node_modules/scheduler/cjs/scheduler.development.js", "lineNumber": 532, "columnNumber": 20}], "parentId": {"id": "556", "debuggerId": "-4999200538019608037.702731756911841636"}}}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/conversations/unread_count", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "accept", "value": "application/json+canvas-string-ids, application/json"}, {"name": "x-csrf-token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "x-requested-with", "value": "XMLHttpRequest"}], "queryString": [], "cookies": [], "headersSize": 1214, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"d44573536073db945b6cd2bf45b93dbf\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=7.65, cache_fetch_hit.active_support;dur=0.04, cache_read_multi.active_support;dur=0.32, start_processing.action_controller;dur=0.05, sql.active_record;dur=2.38, instance.active_record;dur=0.02, instantiation.active_record;dur=0.50, process_action.action_controller;dur=20.23"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=conversations;n=unread_count;b=1672432;m=1672432;u=0.02;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "600.0"}, {"name": "x-request-context-id", "value": "bb7221bc-3d60-4669-b85d-dbfcc0985e65"}, {"name": "x-request-cost", "value": "0.021615541001811067"}, {"name": "x-runtime", "value": "0.400754"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 20, "mimeType": "application/json", "compression": -11, "text": "{\"unread_count\":\"0\"}"}, "redirectURL": "", "headersSize": 1527, "bodySize": 31, "_transferSize": 1558, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.344Z", "time": 416.23400000389665, "timings": {"blocked": 1.8609999899975955, "dns": -1, "ssl": -1, "connect": -1, "send": 0.019000000000000017, "wait": 413.0570000040997, "receive": 1.2970000097993761, "_blocked_queueing": 1.2599999899975955, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871751", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "jQuery.<computed>", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9679, "columnNumber": 16}, {"functionName": "getJSON", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9660, "columnNumber": 16}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 31, "columnNumber": 23}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 716, "columnNumber": 58}, {"functionName": "Calendar", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 869, "columnNumber": 13}, {"functionName": "initializeDelayed", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 74, "columnNumber": 23}, {"functionName": "start", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 71, "columnNumber": 8}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 111, "columnNumber": 100}, {"functionName": "ready", "scriptId": "25505", "url": "webpack-internal:///./packages/ready/index.js", "lineNumber": 58, "columnNumber": 11}, {"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 56, "columnNumber": 57}, {"functionName": "./ui/features/calendar/index.jsx", "scriptId": "26912", "url": "http://canvas.docker/dist/webpack-dev/node_modules_graphql-tag_lib_index_js-ui_features_calendar_index_jsx-chunk-645b4ea3ed46aa17.js", "lineNumber": 731, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "calendar", "scriptId": "26584", "url": "webpack-internal:///./ui/featureBundles.ts", "lineNumber": 102, "columnNumber": 3643}, {"functionName": "loadBundle", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 72, "columnNumber": 71}, {"functionName": "up", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 96, "columnNumber": 23}, {"functionName": "eval", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 76}, {"functionName": "applyWithoutFail", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 67, "columnNumber": 15}, {"functionName": "up", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 126}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 83, "columnNumber": 50}, {"functionName": "./ui/index.ts", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 8943, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12219, "columnNumber": 90}, {"functionName": "__webpack_require__.O", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12062, "columnNumber": 11}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12220, "columnNumber": 42}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12221, "columnNumber": 2}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/users/4/colors/", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [], "cookies": [], "headersSize": 1233, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"a907e4263e1711b42ad7ec363dbf6226\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=10.22, cache_fetch_hit.active_support;dur=0.06, cache_read_multi.active_support;dur=0.42, start_processing.action_controller;dur=0.04, sql.active_record;dur=4.93, instance.active_record;dur=0.04, instantiation.active_record;dur=2.19, process_action.action_controller;dur=34.84"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=users;n=get_custom_colors;b=1641700;m=1641700;u=0.04;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "450.33612685738"}, {"name": "x-request-context-id", "value": "b91d524b-5408-469e-afd2-84e60783b3ce"}, {"name": "x-request-cost", "value": "0.03855270805140876"}, {"name": "x-runtime", "value": "0.412295"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 80, "mimeType": "application/json", "compression": -11, "text": "{\"custom_colors\":{\"user_4\":\"#4a76b2\",\"course_2\":\"#4f8045\",\"course_1\":\"#91349B\"}}"}, "redirectURL": "", "headersSize": 1536, "bodySize": 91, "_transferSize": 1627, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.413Z", "time": 446.6369999863673, "timings": {"blocked": 7.855999994374812, "dns": -1, "ssl": -1, "connect": -1, "send": 0.11699999999999999, "wait": 437.871000007296, "receive": 0.7929999846965075, "_blocked_queueing": 5.334999994374812, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871548", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.refetchAllSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8972, "columnNumber": 26}, {"functionName": "Calendar.refetchEvents", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11382, "columnNumber": 26}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14404, "columnNumber": 46}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "$.fn.fullCalendar", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14383, "columnNumber": 9}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 624, "columnNumber": 34}, {"functionName": "wrapper", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 38, "columnNumber": 14}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "eval", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8709, "columnNumber": 16}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8708, "columnNumber": 14}, {"functionName": "publish", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 58, "columnNumber": 12}, {"functionName": "eval", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 109, "columnNumber": 58}, {"functionName": "VisibleContextManager", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 150, "columnNumber": 13}, {"functionName": "sidebar", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 289, "columnNumber": 26}, {"functionName": "initializeDelayed", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 105, "columnNumber": 67}, {"functionName": "start", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 71, "columnNumber": 8}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 111, "columnNumber": 100}, {"functionName": "ready", "scriptId": "25505", "url": "webpack-internal:///./packages/ready/index.js", "lineNumber": 58, "columnNumber": 11}, {"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 56, "columnNumber": 57}, {"functionName": "./ui/features/calendar/index.jsx", "scriptId": "26912", "url": "http://canvas.docker/dist/webpack-dev/node_modules_graphql-tag_lib_index_js-ui_features_calendar_index_jsx-chunk-645b4ea3ed46aa17.js", "lineNumber": 731, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "calendar", "scriptId": "26584", "url": "webpack-internal:///./ui/featureBundles.ts", "lineNumber": 102, "columnNumber": 3643}, {"functionName": "loadBundle", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 72, "columnNumber": 71}, {"functionName": "up", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 96, "columnNumber": 23}, {"functionName": "eval", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 76}, {"functionName": "applyWithoutFail", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 67, "columnNumber": 15}, {"functionName": "up", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 126}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 83, "columnNumber": 50}, {"functionName": "./ui/index.ts", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 8943, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12219, "columnNumber": 90}, {"functionName": "__webpack_require__.O", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12062, "columnNumber": 11}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12220, "columnNumber": 42}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12221, "columnNumber": 2}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-08-31T18%3A30%3A00.000Z&end_date=2025-10-05T18%3A30%3A00.000Z&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-10-05T18%3A30%3A00.000Z"}, {"name": "include%5B%5D", "value": "web_conference"}, {"name": "include%5B%5D", "value": "series_head"}, {"name": "include%5B%5D", "value": "series_natural_language"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1501, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:19 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"847745d364783fef14c93900be8c2d3e\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&start_date=2025-08-31T18%3A30%3A00.000Z&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&start_date=2025-08-31T18%3A30%3A00.000Z&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&start_date=2025-08-31T18%3A30%3A00.000Z&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=11.07, cache_fetch_hit.active_support;dur=0.06, cache_read_multi.active_support;dur=0.21, start_processing.action_controller;dur=0.02, sql.active_record;dur=10.54, instance.active_record;dur=0.25, instantiation.active_record;dur=7.37, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.21, process_action.action_controller;dur=65.10"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1672432;m=1672432;u=0.06;y=0.00;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "600.0"}, {"name": "x-request-context-id", "value": "21d5ba35-8113-4651-b648-c1f85d407112"}, {"name": "x-request-cost", "value": "0.06283262697598424"}, {"name": "x-runtime", "value": "0.510201"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2982, "mimeType": "application/json", "compression": -12, "text": "[{\"id\":\"5\",\"title\":\"TEST 1\",\"start_at\":\"2025-09-03T19:15:00Z\",\"end_at\":\"2025-09-03T22:30:00Z\",\"workflow_state\":\"active\",\"created_at\":\"2025-09-03T04:52:52Z\",\"updated_at\":\"2025-09-04T07:08:06Z\",\"all_day\":false,\"all_day_date\":\"2025-09-04\",\"comments\":null,\"rrule\":\"\",\"series_uuid\":null,\"blackout_date\":false,\"location_address\":null,\"location_name\":\"\",\"type\":\"event\",\"description\":null,\"child_events_count\":0,\"all_context_codes\":\"user_4\",\"context_code\":\"user_4\",\"context_name\":\"Student 1\",\"context_color\":null,\"parent_event_id\":null,\"hidden\":false,\"child_events\":[],\"url\":\"http://canvas.docker/api/v1/calendar_events/5\",\"html_url\":\"http://canvas.docker/calendar?event_id=5\\u0026include_contexts=user_4\",\"duplicates\":[],\"important_dates\":false},{\"id\":\"84\",\"title\":\"Deserunt eum explica\",\"start_at\":\"2025-09-03T19:15:00Z\",\"end_at\":\"2025-09-03T21:00:00Z\",\"workflow_state\":\"active\",\"created_at\":\"2025-09-04T07:08:14Z\",\"updated_at\":\"2025-09-04T07:08:14Z\",\"all_day\":false,\"all_day_date\":null,\"comments\":null,\"rrule\":\"\",\"series_uuid\":null,\"blackout_date\":false,\"location_address\":null,\"location_name\":\"\",\"type\":\"event\",\"description\":null,\"child_events_count\":0,\"all_context_codes\":\"user_4\",\"context_code\":\"user_4\",\"context_name\":\"Student 1\",\"context_color\":null,\"parent_event_id\":null,\"hidden\":false,\"child_events\":[],\"url\":\"http://canvas.docker/api/v1/calendar_events/84\",\"html_url\":\"http://canvas.docker/calendar?event_id=84\\u0026include_contexts=user_4\",\"duplicates\":[],\"important_dates\":false},{\"id\":\"83\",\"title\":\"Test Event M stridelearning.beta\",\"start_at\":\"2025-09-04T07:07:43Z\",\"end_at\":\"2025-09-04T07:07:43Z\",\"workflow_state\":\"active\",\"created_at\":\"2025-09-04T07:07:43Z\",\"updated_at\":\"2025-09-04T07:07:43Z\",\"all_day\":false,\"all_day_date\":null,\"comments\":null,\"rrule\":\"\",\"series_uuid\":null,\"blackout_date\":false,\"location_address\":null,\"location_name\":\"\",\"type\":\"event\",\"description\":null,\"child_events_count\":0,\"all_context_codes\":\"user_4\",\"context_code\":\"user_4\",\"context_name\":\"Student 1\",\"context_color\":null,\"parent_event_id\":null,\"hidden\":false,\"child_events\":[],\"url\":\"http://canvas.docker/api/v1/calendar_events/83\",\"html_url\":\"http://canvas.docker/calendar?event_id=83\\u0026include_contexts=user_4\",\"duplicates\":[],\"important_dates\":false},{\"id\":\"6\",\"title\":\"Test 2\",\"start_at\":\"2025-09-05T18:30:00Z\",\"end_at\":\"2025-09-05T18:30:00Z\",\"workflow_state\":\"active\",\"created_at\":\"2025-09-03T04:52:56Z\",\"updated_at\":\"2025-09-03T04:52:56Z\",\"all_day\":true,\"all_day_date\":\"2025-09-06\",\"comments\":null,\"rrule\":\"\",\"series_uuid\":null,\"blackout_date\":false,\"location_address\":null,\"location_name\":\"\",\"type\":\"event\",\"description\":null,\"child_events_count\":0,\"all_context_codes\":\"user_4\",\"context_code\":\"user_4\",\"context_name\":\"Student 1\",\"context_color\":null,\"parent_event_id\":null,\"hidden\":false,\"child_events\":[],\"url\":\"http://canvas.docker/api/v1/calendar_events/6\",\"html_url\":\"http://canvas.docker/calendar?event_id=6\\u0026include_contexts=user_4\",\"duplicates\":[],\"important_dates\":false}]"}, "redirectURL": "", "headersSize": 2630, "bodySize": 2994, "_transferSize": 5624, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.413Z", "time": 862.8040000039618, "timings": {"blocked": 347.5349999932181, "dns": -1, "ssl": -1, "connect": -1, "send": 0.08899999999999864, "wait": 513.3410000066328, "receive": 1.8390000041108578, "_blocked_queueing": 5.567999993218109, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871537", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.refetchAllSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8972, "columnNumber": 26}, {"functionName": "Calendar.refetchEvents", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11382, "columnNumber": 26}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14404, "columnNumber": 46}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "$.fn.fullCalendar", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14383, "columnNumber": 9}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 624, "columnNumber": 34}, {"functionName": "wrapper", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 38, "columnNumber": 14}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "eval", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8709, "columnNumber": 16}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8708, "columnNumber": 14}, {"functionName": "publish", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 58, "columnNumber": 12}, {"functionName": "eval", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 109, "columnNumber": 58}, {"functionName": "VisibleContextManager", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 150, "columnNumber": 13}, {"functionName": "sidebar", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 289, "columnNumber": 26}, {"functionName": "initializeDelayed", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 105, "columnNumber": 67}, {"functionName": "start", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 71, "columnNumber": 8}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 111, "columnNumber": 100}, {"functionName": "ready", "scriptId": "25505", "url": "webpack-internal:///./packages/ready/index.js", "lineNumber": 58, "columnNumber": 11}, {"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 56, "columnNumber": 57}, {"functionName": "./ui/features/calendar/index.jsx", "scriptId": "26912", "url": "http://canvas.docker/dist/webpack-dev/node_modules_graphql-tag_lib_index_js-ui_features_calendar_index_jsx-chunk-645b4ea3ed46aa17.js", "lineNumber": 731, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "calendar", "scriptId": "26584", "url": "webpack-internal:///./ui/featureBundles.ts", "lineNumber": 102, "columnNumber": 3643}, {"functionName": "loadBundle", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 72, "columnNumber": 71}, {"functionName": "up", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 96, "columnNumber": 23}, {"functionName": "eval", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 76}, {"functionName": "applyWithoutFail", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 67, "columnNumber": 15}, {"functionName": "up", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 126}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 83, "columnNumber": 50}, {"functionName": "./ui/index.ts", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 8943, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12219, "columnNumber": 90}, {"functionName": "__webpack_require__.O", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12062, "columnNumber": 11}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12220, "columnNumber": 42}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12221, "columnNumber": 2}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-08-31T18%3A30%3A00.000Z&end_date=2025-10-05T18%3A30%3A00.000Z&type=assignment&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-10-05T18%3A30%3A00.000Z"}, {"name": "type", "value": "assignment"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1424, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:19 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"2a5e6ab0ba00cdcda3c59d4528b0418e\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&start_date=2025-08-31T18%3A30%3A00.000Z&type=assignment&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&start_date=2025-08-31T18%3A30%3A00.000Z&type=assignment&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&start_date=2025-08-31T18%3A30%3A00.000Z&type=assignment&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=30.26, cache_fetch_hit.active_support;dur=0.14, cache_read_multi.active_support;dur=0.96, start_processing.action_controller;dur=0.02, sql.active_record;dur=59.90, instance.active_record;dur=2.60, instantiation.active_record;dur=34.28, start_transaction.active_record;dur=0.00, transaction.active_record;dur=2.60, process_action.action_controller;dur=400.91"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1639596;m=1639596;u=0.36;y=0.01;d=0.03;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "553.417522712743"}, {"name": "x-request-context-id", "value": "deddf6b8-95f5-4d22-afab-e8e9aa8db174"}, {"name": "x-request-cost", "value": "0.38913566513723996"}, {"name": "x-runtime", "value": "0.780067"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 29660, "mimeType": "application/json", "compression": -13, "text": "[{\"title\":\"assignment1 Ungraded\",\"description\":null,\"submission_types\":\"online_text_entry,online_upload\",\"workflow_state\":\"published\",\"created_at\":\"2025-09-04T07:09:42Z\",\"updated_at\":\"2025-09-04T07:09:58Z\",\"all_day\":false,\"all_day_date\":\"2025-09-03\",\"id\":\"assignment_11\",\"type\":\"assignment\",\"assignment\":{\"id\":\"11\",\"description\":null,\"due_at\":\"2025-09-03T05:59:00Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"2\",\"grading_standard_id\":null,\"created_at\":\"2025-09-04T07:09:42Z\",\"updated_at\":\"2025-09-04T07:09:58Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":4,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6ImZkMzdjMWYxLTViODgtNDkzYy05MjFkLTU1ZWQxMmI2YzY0YyJ9.JWspQWrvQHEd1rEAQ3qs1I5LHuYYayp_c_3kF8TLF78\",\"lti_context_id\":\"fd37c1f1-5b88-493c-921d-55ed12b6c64c\",\"course_id\":\"1\",\"name\":\"assignment1 Ungraded\",\"submission_types\":[\"online_text_entry\",\"online_upload\"],\"has_submitted_submissions\":true,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":true,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":\"1\",\"original_assignment_id\":\"6\",\"original_lti_resource_link_id\":null,\"original_assignment_name\":\"assignment1 Group\",\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/1/assignments/11\",\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/1/assignments/11/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/1/assignments/11\",\"context_code\":\"course_1\",\"context_name\":\"Shard1 Course\",\"context_color\":null,\"end_at\":\"2025-09-03T05:59:00Z\",\"start_at\":\"2025-09-03T05:59:00Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_11\",\"important_dates\":false},{\"title\":\"assignment1 Graded\",\"description\":null,\"submission_types\":\"online_text_entry,online_upload\",\"workflow_state\":\"published\",\"created_at\":\"2025-09-04T07:10:01Z\",\"updated_at\":\"2025-09-04T07:11:12Z\",\"all_day\":false,\"all_day_date\":\"2025-09-03\",\"id\":\"assignment_12\",\"type\":\"assignment\",\"assignment\":{\"id\":\"12\",\"description\":null,\"due_at\":\"2025-09-03T05:59:00Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"2\",\"grading_standard_id\":null,\"created_at\":\"2025-09-04T07:10:01Z\",\"updated_at\":\"2025-09-04T07:11:12Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":5,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6ImJhMzgyYjEzLTQyOWItNDZkNC04MGFjLWVkYzQwMjkwOTczOSJ9.UZOdfmJM-zc7Cc-DSM7PlZxXZxLwZyaji_db7bZ1O4M\",\"lti_context_id\":\"ba382b13-429b-46d4-80ac-edc402909739\",\"course_id\":\"1\",\"name\":\"assignment1 Graded\",\"submission_types\":[\"online_text_entry\",\"online_upload\"],\"has_submitted_submissions\":true,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":true,\"user_submitted\":true,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":\"1\",\"original_assignment_id\":\"11\",\"original_lti_resource_link_id\":null,\"original_assignment_name\":\"assignment1 Ungraded\",\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":false,\"html_url\":\"http://canvas.docker/courses/1/assignments/12\",\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/1/assignments/12/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/1/assignments/12\",\"context_code\":\"course_1\",\"context_name\":\"Shard1 Course\",\"context_color\":null,\"end_at\":\"2025-09-03T05:59:00Z\",\"start_at\":\"2025-09-03T05:59:00Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_12\",\"important_dates\":false},{\"title\":\"discussion1\",\"submission_types\":\"discussion_topic\",\"workflow_state\":\"published\",\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-09-03T04:53:49Z\",\"all_day\":false,\"all_day_date\":\"2025-09-05\",\"lock_info\":{\"asset_string\":\"discussion_topic_1\",\"context_module\":{\"id\":\"2\",\"name\":\"Discussion\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"description\":null,\"id\":\"assignment_2\",\"type\":\"assignment\",\"assignment\":{\"id\":\"2\",\"due_at\":\"2025-09-05T05:59:59Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"1\",\"grading_standard_id\":null,\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-09-03T04:53:49Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":1,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"lock_info\":{\"asset_string\":\"discussion_topic_1\",\"context_module\":{\"id\":\"2\",\"name\":\"Discussion\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjlmZTFlNGUxLTY1MDctNDcxYS05NjZlLTgwZTc5MGE2Y2MzNyJ9.Zn3zZZz4o2GJx1kCMQeur5InjBGnnfpS1P0T21-jY5I\",\"lti_context_id\":\"9fe1e4e1-6507-471a-966e-80e790a6cc37\",\"course_id\":\"2\",\"name\":\"discussion1\",\"submission_types\":[\"discussion_topic\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"description\":null,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/2/assignments/2\",\"discussion_topic\":{\"id\":\"1\",\"title\":\"discussion1\",\"last_reply_at\":\"2025-08-18T12:32:45Z\",\"created_at\":\"2025-08-18T12:32:44Z\",\"delayed_post_at\":null,\"posted_at\":\"2025-08-18T12:32:45Z\",\"assignment_id\":\"2\",\"root_topic_id\":null,\"position\":null,\"podcast_has_student_posts\":false,\"discussion_type\":\"side_comment\",\"lock_at\":null,\"allow_rating\":false,\"only_graders_can_rate\":false,\"sort_by_rating\":false,\"is_section_specific\":false,\"anonymous_state\":null,\"summary_enabled\":false,\"user_name\":\"<EMAIL>\",\"discussion_subentry_count\":0,\"permissions\":{\"attach\":true,\"update\":false,\"reply\":false,\"delete\":false,\"manage_assign_to\":false},\"require_initial_post\":null,\"user_can_see_posts\":true,\"podcast_url\":null,\"read_state\":\"unread\",\"unread_count\":0,\"subscribed\":false,\"attachments\":[],\"published\":true,\"can_unpublish\":false,\"locked\":false,\"can_lock\":false,\"comments_disabled\":false,\"author\":{\"id\":\"1\",\"anonymous_id\":\"1\",\"display_name\":\"<EMAIL>\",\"avatar_image_url\":\"http://canvas.instructure.com/images/messages/avatar-50.png\",\"html_url\":\"http://canvas.docker/courses/2/users/1\",\"pronouns\":null},\"html_url\":\"http://canvas.docker/courses/2/discussion_topics/1\",\"url\":\"http://canvas.docker/courses/2/discussion_topics/1\",\"pinned\":false,\"group_category_id\":null,\"can_group\":true,\"topic_children\":[],\"group_topic_children\":[],\"locked_for_user\":true,\"lock_info\":{\"asset_string\":\"discussion_topic_1\",\"context_module\":{\"id\":\"2\",\"name\":\"Discussion\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"lock_explanation\":\"This topic is part of the module \\u003cb\\u003eDiscussion\\u003c/b\\u003e and hasn\\u0026#39;t been unlocked yet.\\u003cbr/\\u003e\\u003cdiv class='spinner'\\u003e\\u003c/div\\u003e\\u003ca style='display: none;' class='module_prerequisites_fallback' href='http://canvas.docker/courses/2/modules#module_2'\\u003eVisit the course modules page for information on how to unlock this content.\\u003c/a\\u003e\\u003ca x-canvaslms-trusted-url='/courses/2/modules/2/prerequisites/discussion_topic_1' style='display: none;' id='module_prerequisites_lookup_link'\\u003e\\u0026nbsp;\\u003c/a\\u003e\",\"message\":\"This topic is part of the module \\u003cb\\u003eDiscussion\\u003c/b\\u003e and hasn\\u0026#39;t been unlocked yet.\\u003cbr/\\u003e\\u003cdiv class='spinner'\\u003e\\u003c/div\\u003e\\u003ca style='display: none;' class='module_prerequisites_fallback' href='http://canvas.docker/courses/2/modules#module_2'\\u003eVisit the course modules page for information on how to unlock this content.\\u003c/a\\u003e\\u003ca x-canvaslms-trusted-url='/courses/2/modules/2/prerequisites/discussion_topic_1' style='display: none;' id='module_prerequisites_lookup_link'\\u003e\\u0026nbsp;\\u003c/a\\u003e\",\"todo_date\":null,\"is_announcement\":false,\"sort_order\":\"desc\",\"sort_order_locked\":false,\"expanded\":false,\"expanded_locked\":false},\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":true,\"lock_explanation\":\"This assignment is part of the module \\u003cb\\u003eDiscussion\\u003c/b\\u003e and hasn\\u0026#39;t been unlocked yet.\\u003cbr/\\u003e\\u003cdiv class='spinner'\\u003e\\u003c/div\\u003e\\u003ca style='display: none;' class='module_prerequisites_fallback' href='http://canvas.docker/courses/2/modules#module_2'\\u003eVisit the course modules page for information on how to unlock this content.\\u003c/a\\u003e\\u003ca x-canvaslms-trusted-url='/courses/2/modules/2/prerequisites/discussion_topic_1' style='display: none;' id='module_prerequisites_lookup_link'\\u003e\\u0026nbsp;\\u003c/a\\u003e\",\"submissions_download_url\":\"http://canvas.docker/courses/2/assignments/2/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/2/assignments/2\",\"context_code\":\"course_2\",\"context_name\":\"Course 1\",\"context_color\":null,\"end_at\":\"2025-09-05T05:59:59Z\",\"start_at\":\"2025-09-05T05:59:59Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_2\",\"important_dates\":false},{\"title\":\"assignment1 Group\",\"description\":\"\",\"submission_types\":\"online_text_entry,online_upload\",\"workflow_state\":\"published\",\"created_at\":\"2025-09-04T07:06:38Z\",\"updated_at\":\"2025-09-04T07:21:23Z\",\"all_day\":false,\"all_day_date\":\"2025-09-11\",\"id\":\"assignment_6\",\"type\":\"assignment\",\"assignment\":{\"id\":\"6\",\"description\":\"\",\"due_at\":\"2025-09-11T05:59:00Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"3\",\"grading_standard_id\":null,\"created_at\":\"2025-09-04T07:06:38Z\",\"updated_at\":\"2025-09-04T07:21:23Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":1,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjJjZGZhNmMxLTFhNGQtNDAyZS04ZDI3LWY0NDE1ZTg4ODg0NSJ9.G393Sz3Td-tTDnwpr8jFOsoWOcDfWwo8zEjhlmZZApI\",\"lti_context_id\":\"2cdfa6c1-1a4d-402e-8d27-f4415e888845\",\"course_id\":\"1\",\"name\":\"assignment1 Group\",\"submission_types\":[\"online_text_entry\",\"online_upload\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/1/assignments/6\",\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/1/assignments/6/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/1/assignments/6\",\"context_code\":\"course_1\",\"context_name\":\"Shard1 Course\",\"context_color\":null,\"end_at\":\"2025-09-11T05:59:00Z\",\"start_at\":\"2025-09-11T05:59:00Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_6\",\"important_dates\":false},{\"title\":\"assignment1\",\"description\":\"\",\"submission_types\":\"online_text_entry,online_upload\",\"workflow_state\":\"published\",\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-09-03T04:53:59Z\",\"all_day\":false,\"all_day_date\":\"2025-09-12\",\"id\":\"assignment_3\",\"type\":\"assignment\",\"assignment\":{\"id\":\"3\",\"description\":\"\",\"due_at\":\"2025-09-12T05:59:00Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"1\",\"grading_standard_id\":null,\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-09-03T04:53:59Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":1,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjNiMTI4ZWE2LTA5ZTAtNDhhZS1hNmUwLTgzNWQ1MGFmODAwZSJ9.ADaw7iJ4ZVO7RX_vEjV6ItUqWVPuQNdglu1We2uFfAs\",\"lti_context_id\":\"3b128ea6-09e0-48ae-a6e0-835d50af800e\",\"course_id\":\"2\",\"name\":\"assignment1\",\"submission_types\":[\"online_text_entry\",\"online_upload\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/2/assignments/3\",\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/2/assignments/3/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/2/assignments/3\",\"context_code\":\"course_2\",\"context_name\":\"Course 1\",\"context_color\":null,\"end_at\":\"2025-09-12T05:59:00Z\",\"start_at\":\"2025-09-12T05:59:00Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_3\",\"important_dates\":false},{\"title\":\"discussion1\",\"description\":\"Test Discussion\",\"submission_types\":\"discussion_topic\",\"workflow_state\":\"published\",\"created_at\":\"2025-09-04T07:06:39Z\",\"updated_at\":\"2025-09-04T07:07:12Z\",\"all_day\":false,\"all_day_date\":\"2025-09-16\",\"id\":\"assignment_9\",\"type\":\"assignment\",\"assignment\":{\"id\":\"9\",\"description\":\"Test Discussion\",\"due_at\":\"2025-09-16T05:59:00Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"2\",\"grading_standard_id\":null,\"created_at\":\"2025-09-04T07:06:39Z\",\"updated_at\":\"2025-09-04T07:07:12Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":8,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjMyNDU4NzUxLTgxNWItNGQ1MC1iZjI2LTQ2MTg2Yjc1NDdjMSIsImx0aV9hc3NpZ25tZW50X2Rlc2NyaXB0aW9uIjoiVGVzdCBEaXNjdXNzaW9uIn0.FLw-bebU7u0zspEKYPwtaQObGDxFwUnIBTW00e-c8oc\",\"lti_context_id\":\"32458751-815b-4d50-bf26-46186b7547c1\",\"course_id\":\"1\",\"name\":\"discussion1\",\"submission_types\":[\"discussion_topic\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/1/assignments/9\",\"discussion_topic\":{\"id\":\"2\",\"title\":\"discussion1\",\"last_reply_at\":\"2025-09-04T07:06:39Z\",\"created_at\":\"2025-09-04T07:06:39Z\",\"delayed_post_at\":null,\"posted_at\":\"2025-09-04T07:06:39Z\",\"assignment_id\":\"9\",\"root_topic_id\":null,\"position\":null,\"podcast_has_student_posts\":false,\"discussion_type\":\"threaded\",\"lock_at\":null,\"allow_rating\":false,\"only_graders_can_rate\":false,\"sort_by_rating\":false,\"is_section_specific\":false,\"anonymous_state\":null,\"summary_enabled\":false,\"user_name\":\"<EMAIL>\",\"discussion_subentry_count\":0,\"permissions\":{\"attach\":true,\"update\":false,\"reply\":true,\"delete\":false,\"manage_assign_to\":false},\"require_initial_post\":null,\"user_can_see_posts\":true,\"podcast_url\":null,\"read_state\":\"unread\",\"unread_count\":0,\"subscribed\":false,\"attachments\":[],\"published\":true,\"can_unpublish\":false,\"locked\":false,\"can_lock\":false,\"comments_disabled\":false,\"author\":{\"id\":\"1\",\"anonymous_id\":\"1\",\"display_name\":\"<EMAIL>\",\"avatar_image_url\":\"http://canvas.instructure.com/images/messages/avatar-50.png\",\"html_url\":\"http://canvas.docker/courses/1/users/1\",\"pronouns\":null},\"html_url\":\"http://canvas.docker/courses/1/discussion_topics/2\",\"url\":\"http://canvas.docker/courses/1/discussion_topics/2\",\"pinned\":false,\"group_category_id\":null,\"can_group\":true,\"topic_children\":[],\"group_topic_children\":[],\"locked_for_user\":false,\"message\":\"Test Discussion\",\"todo_date\":null,\"is_announcement\":false,\"sort_order\":\"desc\",\"sort_order_locked\":false,\"expanded\":false,\"expanded_locked\":false},\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/1/assignments/9/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/1/assignments/9\",\"context_code\":\"course_1\",\"context_name\":\"Shard1 Course\",\"context_color\":null,\"end_at\":\"2025-09-16T05:59:00Z\",\"start_at\":\"2025-09-16T05:59:00Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_9\",\"important_dates\":false},{\"title\":\"GradedQuiz1\",\"description\":null,\"submission_types\":\"online_quiz\",\"workflow_state\":\"published\",\"created_at\":\"2025-09-04T07:06:38Z\",\"updated_at\":\"2025-09-04T07:06:58Z\",\"all_day\":false,\"all_day_date\":\"2025-09-20\",\"id\":\"assignment_7\",\"type\":\"assignment\",\"assignment\":{\"id\":\"7\",\"description\":null,\"due_at\":\"2025-09-20T05:59:00Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":58.0,\"grading_type\":\"points\",\"assignment_group_id\":\"2\",\"grading_standard_id\":null,\"created_at\":\"2025-09-04T07:06:38Z\",\"updated_at\":\"2025-09-04T07:06:58Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":1,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjJiZTUwMzlhLWU5NjktNGFmMC1iOWE5LTkxMTU1NjNkNjAxZSJ9.Ukr7xe951KqFHe3j9oyCC8lKi-0-Y2nTC2VjLSZLxiU\",\"lti_context_id\":\"2be5039a-e969-4af0-b9a9-9115563d601e\",\"course_id\":\"1\",\"name\":\"GradedQuiz1\",\"submission_types\":[\"online_quiz\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":true,\"can_duplicate\":false,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/1/assignments/7\",\"quiz_id\":\"7\",\"anonymous_submissions\":false,\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/1/quizzes/7/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/1/assignments/7\",\"context_code\":\"course_1\",\"context_name\":\"Shard1 Course\",\"context_color\":null,\"end_at\":\"2025-09-20T05:59:00Z\",\"start_at\":\"2025-09-20T05:59:00Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_7\",\"important_dates\":false},{\"title\":\"GradedQuiz1\",\"submission_types\":\"online_quiz\",\"workflow_state\":\"published\",\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-09-03T04:54:14Z\",\"all_day\":false,\"all_day_date\":\"2025-09-20\",\"lock_info\":{\"asset_string\":\"quizzes:quiz_1\",\"context_module\":{\"id\":\"3\",\"name\":\"Quiz\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"description\":null,\"id\":\"assignment_1\",\"type\":\"assignment\",\"assignment\":{\"id\":\"1\",\"due_at\":\"2025-09-20T05:59:59Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":58.0,\"grading_type\":\"points\",\"assignment_group_id\":\"1\",\"grading_standard_id\":null,\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-09-03T04:54:14Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":1,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"lock_info\":{\"asset_string\":\"quizzes:quiz_1\",\"context_module\":{\"id\":\"3\",\"name\":\"Quiz\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6ImM5MmY5YTE5LTcyMjctNGJiZi1hNzA3LWIzZDk1ODYwNDRjMiJ9.68_9V67FphNDqxlbS19_KecX9LKWwuyRTV-SszSHGZQ\",\"lti_context_id\":\"c92f9a19-7227-4bbf-a707-b3d9586044c2\",\"course_id\":\"2\",\"name\":\"GradedQuiz1\",\"submission_types\":[\"online_quiz\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":true,\"can_duplicate\":false,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"description\":null,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/2/assignments/1\",\"quiz_id\":\"1\",\"anonymous_submissions\":false,\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":true,\"lock_explanation\":\"This assignment is part of the module \\u003cb\\u003eQuiz\\u003c/b\\u003e and hasn\\u0026#39;t been unlocked yet.\\u003cbr/\\u003e\\u003cdiv class='spinner'\\u003e\\u003c/div\\u003e\\u003ca style='display: none;' class='module_prerequisites_fallback' href='http://canvas.docker/courses/2/modules#module_3'\\u003eVisit the course modules page for information on how to unlock this content.\\u003c/a\\u003e\\u003ca x-canvaslms-trusted-url='/courses/2/modules/3/prerequisites/quizzes:quiz_1' style='display: none;' id='module_prerequisites_lookup_link'\\u003e\\u0026nbsp;\\u003c/a\\u003e\",\"submissions_download_url\":\"http://canvas.docker/courses/2/quizzes/1/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/2/assignments/1\",\"context_code\":\"course_2\",\"context_name\":\"Course 1\",\"context_color\":null,\"end_at\":\"2025-09-20T05:59:59Z\",\"start_at\":\"2025-09-20T05:59:59Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_1\",\"important_dates\":false},{\"title\":\"GradedSurvey1\",\"description\":null,\"submission_types\":\"online_quiz\",\"workflow_state\":\"published\",\"created_at\":\"2025-09-04T07:06:38Z\",\"updated_at\":\"2025-09-04T07:07:04Z\",\"all_day\":false,\"all_day_date\":\"2025-09-21\",\"id\":\"assignment_8\",\"type\":\"assignment\",\"assignment\":{\"id\":\"8\",\"description\":null,\"due_at\":\"2025-09-21T05:59:00Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":null,\"grading_type\":\"points\",\"assignment_group_id\":\"2\",\"grading_standard_id\":null,\"created_at\":\"2025-09-04T07:06:38Z\",\"updated_at\":\"2025-09-04T07:07:04Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":7,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjcyMTZjYWM2LWU1YTctNGY5Ny1hMmYyLTE2N2Q0MTkzZjA4NyJ9.zL7pXyNxnq8Z1YgdmcJLs3k7FZBazGiEpB05DGLl8EE\",\"lti_context_id\":\"7216cac6-e5a7-4f97-a2f2-167d4193f087\",\"course_id\":\"1\",\"name\":\"GradedSurvey1\",\"submission_types\":[\"online_quiz\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":false,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/1/assignments/8\",\"quiz_id\":\"8\",\"anonymous_submissions\":false,\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/1/quizzes/8/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/1/assignments/8\",\"context_code\":\"course_1\",\"context_name\":\"Shard1 Course\",\"context_color\":null,\"end_at\":\"2025-09-21T05:59:00Z\",\"start_at\":\"2025-09-21T05:59:00Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_8\",\"important_dates\":false}]"}, "redirectURL": "", "headersSize": 2404, "bodySize": 29673, "_transferSize": 32077, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.413Z", "time": 814.7380000154953, "timings": {"blocked": 7.85300000067614, "dns": -1, "ssl": -1, "connect": -1, "send": 0.041999999999999815, "wait": 804.7780000069588, "receive": 2.0650000078603625, "_blocked_queueing": 5.67600000067614, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871746", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.refetchAllSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8972, "columnNumber": 26}, {"functionName": "Calendar.refetchEvents", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11382, "columnNumber": 26}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14404, "columnNumber": 46}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "$.fn.fullCalendar", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14383, "columnNumber": 9}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 624, "columnNumber": 34}, {"functionName": "wrapper", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 38, "columnNumber": 14}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "eval", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8709, "columnNumber": 16}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8708, "columnNumber": 14}, {"functionName": "publish", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 58, "columnNumber": 12}, {"functionName": "eval", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 109, "columnNumber": 58}, {"functionName": "VisibleContextManager", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 150, "columnNumber": 13}, {"functionName": "sidebar", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 289, "columnNumber": 26}, {"functionName": "initializeDelayed", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 105, "columnNumber": 67}, {"functionName": "start", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 71, "columnNumber": 8}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 111, "columnNumber": 100}, {"functionName": "ready", "scriptId": "25505", "url": "webpack-internal:///./packages/ready/index.js", "lineNumber": 58, "columnNumber": 11}, {"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 56, "columnNumber": 57}, {"functionName": "./ui/features/calendar/index.jsx", "scriptId": "26912", "url": "http://canvas.docker/dist/webpack-dev/node_modules_graphql-tag_lib_index_js-ui_features_calendar_index_jsx-chunk-645b4ea3ed46aa17.js", "lineNumber": 731, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "calendar", "scriptId": "26584", "url": "webpack-internal:///./ui/featureBundles.ts", "lineNumber": 102, "columnNumber": 3643}, {"functionName": "loadBundle", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 72, "columnNumber": 71}, {"functionName": "up", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 96, "columnNumber": 23}, {"functionName": "eval", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 76}, {"functionName": "applyWithoutFail", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 67, "columnNumber": 15}, {"functionName": "up", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 126}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 83, "columnNumber": 50}, {"functionName": "./ui/index.ts", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 8943, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12219, "columnNumber": 90}, {"functionName": "__webpack_require__.O", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12062, "columnNumber": 11}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12220, "columnNumber": 42}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12221, "columnNumber": 2}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-08-31T18%3A30%3A00.000Z&end_date=2025-10-05T18%3A30%3A00.000Z&type=sub_assignment&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-10-05T18%3A30%3A00.000Z"}, {"name": "type", "value": "sub_assignment"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1428, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&start_date=2025-08-31T18%3A30%3A00.000Z&type=sub_assignment&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&start_date=2025-08-31T18%3A30%3A00.000Z&type=sub_assignment&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&start_date=2025-08-31T18%3A30%3A00.000Z&type=sub_assignment&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=27.55, cache_fetch_hit.active_support;dur=0.30, cache_read_multi.active_support;dur=0.51, start_processing.action_controller;dur=0.05, sql.active_record;dur=14.59, instance.active_record;dur=0.29, instantiation.active_record;dur=8.37, start_transaction.active_record;dur=0.00, transaction.active_record;dur=3.94, process_action.action_controller;dur=130.52"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1650152;m=1650152;u=0.09;y=0.02;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "551.545430004529"}, {"name": "x-request-context-id", "value": "3f96bc89-a2ca-438d-9ac6-e37f379167e3"}, {"name": "x-request-cost", "value": "0.09595900096422127"}, {"name": "x-runtime", "value": "0.546515"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2417, "bodySize": 12, "_transferSize": 2429, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.413Z", "time": 581.2710000027437, "timings": {"blocked": 7.847000002227723, "dns": -1, "ssl": -1, "connect": -1, "send": 0.037000000000000144, "wait": 572.5130000102259, "receive": 0.8739999902900308, "_blocked_queueing": 5.917000002227724, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871391", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.refetchAllSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8972, "columnNumber": 26}, {"functionName": "Calendar.refetchEvents", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11382, "columnNumber": 26}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14404, "columnNumber": 46}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "$.fn.fullCalendar", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14383, "columnNumber": 9}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 624, "columnNumber": 34}, {"functionName": "wrapper", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 38, "columnNumber": 14}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "eval", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8709, "columnNumber": 16}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8708, "columnNumber": 14}, {"functionName": "publish", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 58, "columnNumber": 12}, {"functionName": "eval", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 109, "columnNumber": 58}, {"functionName": "VisibleContextManager", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 150, "columnNumber": 13}, {"functionName": "sidebar", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 289, "columnNumber": 26}, {"functionName": "initializeDelayed", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 105, "columnNumber": 67}, {"functionName": "start", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 71, "columnNumber": 8}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 111, "columnNumber": 100}, {"functionName": "ready", "scriptId": "25505", "url": "webpack-internal:///./packages/ready/index.js", "lineNumber": 58, "columnNumber": 11}, {"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 56, "columnNumber": 57}, {"functionName": "./ui/features/calendar/index.jsx", "scriptId": "26912", "url": "http://canvas.docker/dist/webpack-dev/node_modules_graphql-tag_lib_index_js-ui_features_calendar_index_jsx-chunk-645b4ea3ed46aa17.js", "lineNumber": 731, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "calendar", "scriptId": "26584", "url": "webpack-internal:///./ui/featureBundles.ts", "lineNumber": 102, "columnNumber": 3643}, {"functionName": "loadBundle", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 72, "columnNumber": 71}, {"functionName": "up", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 96, "columnNumber": 23}, {"functionName": "eval", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 76}, {"functionName": "applyWithoutFail", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 67, "columnNumber": 15}, {"functionName": "up", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 126}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 83, "columnNumber": 50}, {"functionName": "./ui/index.ts", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 8943, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12219, "columnNumber": 90}, {"functionName": "__webpack_require__.O", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12062, "columnNumber": 11}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12220, "columnNumber": 42}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12221, "columnNumber": 2}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner_notes?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-08-31T18%3A30%3A00.000Z&end_date=2025-10-05T18%3A30%3A00.000Z&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-10-05T18%3A30%3A00.000Z"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1406, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"97f719721b343212692dcb5b3c82e380\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=11.36, cache_fetch_hit.active_support;dur=0.10, cache_read_multi.active_support;dur=0.29, start_processing.action_controller;dur=0.01, sql.active_record;dur=5.48, instance.active_record;dur=0.05, instantiation.active_record;dur=3.24, process_action.action_controller;dur=45.88"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner_notes;n=index;b=1618476;m=1618476;u=0.04;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "500.448866956543"}, {"name": "x-request-context-id", "value": "33f126df-ab42-4403-80be-d2e25f503845"}, {"name": "x-request-cost", "value": "0.04469245698740121"}, {"name": "x-runtime", "value": "0.424501"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 424, "mimeType": "application/json", "compression": -12, "text": "[{\"id\":\"1\",\"todo_date\":\"2025-09-04T18:29:00Z\",\"title\":\"TEST TODO\",\"details\":\"\",\"user_id\":\"4\",\"course_id\":null,\"workflow_state\":\"active\",\"created_at\":\"2025-09-04T07:18:26Z\",\"updated_at\":\"2025-09-04T07:18:26Z\"},{\"id\":\"2\",\"todo_date\":\"2025-09-05T18:29:00Z\",\"title\":\"TEST TODO Undated\",\"details\":\"\",\"user_id\":\"4\",\"course_id\":\"2\",\"workflow_state\":\"active\",\"created_at\":\"2025-09-04T07:18:42Z\",\"updated_at\":\"2025-09-04T07:18:42Z\"}]"}, "redirectURL": "", "headersSize": 1537, "bodySize": 436, "_transferSize": 1973, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.413Z", "time": 464.3809999979567, "timings": {"blocked": 28.795999991768973, "dns": -1, "ssl": -1, "connect": -1, "send": 0.09999999999999787, "wait": 434.0250000066906, "receive": 1.4599999994970858, "_blocked_queueing": 6.186999991768971, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871272", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.refetchAllSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8972, "columnNumber": 26}, {"functionName": "Calendar.refetchEvents", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11382, "columnNumber": 26}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14404, "columnNumber": 46}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "$.fn.fullCalendar", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14383, "columnNumber": 9}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 624, "columnNumber": 34}, {"functionName": "wrapper", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 38, "columnNumber": 14}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "eval", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8709, "columnNumber": 16}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8708, "columnNumber": 14}, {"functionName": "publish", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 58, "columnNumber": 12}, {"functionName": "eval", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 109, "columnNumber": 58}, {"functionName": "VisibleContextManager", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 150, "columnNumber": 13}, {"functionName": "sidebar", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 289, "columnNumber": 26}, {"functionName": "initializeDelayed", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 105, "columnNumber": 67}, {"functionName": "start", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 71, "columnNumber": 8}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 111, "columnNumber": 100}, {"functionName": "ready", "scriptId": "25505", "url": "webpack-internal:///./packages/ready/index.js", "lineNumber": 58, "columnNumber": 11}, {"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 56, "columnNumber": 57}, {"functionName": "./ui/features/calendar/index.jsx", "scriptId": "26912", "url": "http://canvas.docker/dist/webpack-dev/node_modules_graphql-tag_lib_index_js-ui_features_calendar_index_jsx-chunk-645b4ea3ed46aa17.js", "lineNumber": 731, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "calendar", "scriptId": "26584", "url": "webpack-internal:///./ui/featureBundles.ts", "lineNumber": 102, "columnNumber": 3643}, {"functionName": "loadBundle", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 72, "columnNumber": 71}, {"functionName": "up", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 96, "columnNumber": 23}, {"functionName": "eval", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 76}, {"functionName": "applyWithoutFail", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 67, "columnNumber": 15}, {"functionName": "up", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 126}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 83, "columnNumber": 50}, {"functionName": "./ui/index.ts", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 8943, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12219, "columnNumber": 90}, {"functionName": "__webpack_require__.O", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12062, "columnNumber": 11}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12220, "columnNumber": 42}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12221, "columnNumber": 2}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner/items?filter=ungraded_todo_items&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-08-31T18%3A30%3A00.000Z&end_date=2025-10-05T18%3A30%3A00.000Z&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "filter", "value": "ungraded_todo_items"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-10-05T18%3A30%3A00.000Z"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1406, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:19 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"088444f1517b04183cc37d8af52b238a\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&filter=ungraded_todo_items&start_date=2025-08-31T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&filter=ungraded_todo_items&start_date=2025-08-31T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-10-05T18%3A30%3A00.000Z&filter=ungraded_todo_items&start_date=2025-08-31T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=10.11, cache_fetch_hit.active_support;dur=0.05, cache_read_multi.active_support;dur=0.25, start_processing.action_controller;dur=0.01, sql.active_record;dur=6.68, instance.active_record;dur=0.02, instantiation.active_record;dur=1.50, process_action.action_controller;dur=40.09"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner;n=index;b=1803664;m=1803664;u=0.04;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "553.149520146087"}, {"name": "x-request-context-id", "value": "200188b2-d4ac-4472-999d-b9f22c9a816a"}, {"name": "x-request-cost", "value": "0.0397511250740763"}, {"name": "x-runtime", "value": "0.357996"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 917, "mimeType": "application/json", "compression": -12, "text": "[{\"context_type\":\"Course\",\"course_id\":\"1\",\"plannable_id\":\"3\",\"planner_override\":null,\"plannable_type\":\"announcement\",\"new_activity\":true,\"submissions\":false,\"plannable_date\":\"2025-09-04T07:12:14Z\",\"plannable\":{\"id\":\"3\",\"title\":\"Shard 1 Announcement\",\"unread_count\":0,\"read_state\":\"unread\",\"created_at\":\"2025-09-04T07:12:14Z\",\"updated_at\":\"2025-09-04T07:12:31Z\"},\"html_url\":\"/courses/1/discussion_topics/3\",\"context_name\":\"Shard1 Course\",\"context_image\":null},{\"context_type\":\"Course\",\"course_id\":\"1\",\"plannable_id\":\"4\",\"planner_override\":null,\"plannable_type\":\"announcement\",\"new_activity\":true,\"submissions\":false,\"plannable_date\":\"2025-09-04T07:13:34Z\",\"plannable\":{\"id\":\"4\",\"title\":\"Shard 2 Announcement\",\"unread_count\":0,\"read_state\":\"unread\",\"created_at\":\"2025-09-04T07:13:34Z\",\"updated_at\":\"2025-09-04T07:13:34Z\"},\"html_url\":\"/courses/1/discussion_topics/4\",\"context_name\":\"Shard1 Course\",\"context_image\":null}]"}, "redirectURL": "", "headersSize": 2263, "bodySize": 929, "_transferSize": 3192, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.413Z", "time": 745.9920000110287, "timings": {"blocked": 381.1140000152998, "dns": -1, "ssl": -1, "connect": -1, "send": 0.03899999999998727, "wait": 363.55899999896064, "receive": 1.2799999967683107, "_blocked_queueing": 6.278000015299767, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871751", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.refetchAllSources", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8972, "columnNumber": 26}, {"functionName": "Calendar.refetchEvents", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11382, "columnNumber": 26}, {"functionName": "eval", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14404, "columnNumber": 46}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "$.fn.fullCalendar", "scriptId": "27031", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14383, "columnNumber": 9}, {"functionName": "eval", "scriptId": "27027", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 624, "columnNumber": 34}, {"functionName": "wrapper", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 38, "columnNumber": 14}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "eval", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8709, "columnNumber": 16}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 385, "columnNumber": 18}, {"functionName": "each", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 207, "columnNumber": 16}, {"functionName": "trigger", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8708, "columnNumber": 14}, {"functionName": "publish", "scriptId": "26878", "url": "webpack-internal:///./packages/jquery-tinypubsub/index.js", "lineNumber": 58, "columnNumber": 12}, {"functionName": "eval", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 109, "columnNumber": 58}, {"functionName": "VisibleContextManager", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 150, "columnNumber": 13}, {"functionName": "sidebar", "scriptId": "27777", "url": "webpack-internal:///./ui/features/calendar/jquery/sidebar.jsx", "lineNumber": 289, "columnNumber": 26}, {"functionName": "initializeDelayed", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 105, "columnNumber": 67}, {"functionName": "start", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 71, "columnNumber": 8}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 111, "columnNumber": 100}, {"functionName": "ready", "scriptId": "25505", "url": "webpack-internal:///./packages/ready/index.js", "lineNumber": 58, "columnNumber": 11}, {"functionName": "eval", "scriptId": "27026", "url": "webpack-internal:///./ui/features/calendar/index.jsx", "lineNumber": 56, "columnNumber": 57}, {"functionName": "./ui/features/calendar/index.jsx", "scriptId": "26912", "url": "http://canvas.docker/dist/webpack-dev/node_modules_graphql-tag_lib_index_js-ui_features_calendar_index_jsx-chunk-645b4ea3ed46aa17.js", "lineNumber": 731, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "calendar", "scriptId": "26584", "url": "webpack-internal:///./ui/featureBundles.ts", "lineNumber": 102, "columnNumber": 3643}, {"functionName": "loadBundle", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 72, "columnNumber": 71}, {"functionName": "up", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 96, "columnNumber": 23}, {"functionName": "eval", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 76}, {"functionName": "applyWithoutFail", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 67, "columnNumber": 15}, {"functionName": "up", "scriptId": "26355", "url": "webpack-internal:///./node_modules/@instructure/updown/index.js", "lineNumber": 24, "columnNumber": 126}], "parent": {"description": "await", "callFrames": [{"functionName": "eval", "scriptId": "25322", "url": "webpack-internal:///./ui/index.ts", "lineNumber": 83, "columnNumber": 50}, {"functionName": "./ui/index.ts", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 8943, "columnNumber": 0}, {"functionName": "__webpack_require__", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 11794, "columnNumber": 30}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12219, "columnNumber": 90}, {"functionName": "__webpack_require__.O", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12062, "columnNumber": 11}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12220, "columnNumber": 42}, {"functionName": "", "scriptId": "25300", "url": "http://canvas.docker/dist/webpack-dev/main-entry-15c6d7cf29147361.js", "lineNumber": 12221, "columnNumber": 2}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner/items?filter=all_ungraded_todo_items&context_codes%5B%5D=user_4&start_date=2025-08-31T18%3A30%3A00.000Z&end_date=2025-10-05T18%3A30%3A00.000Z&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "kTFtDNQgPu8rRdDo0MJZ+bfgmHWibJQDCdQKT3f6Mx3DBg89ulpdl3oU4YSyjSiajtPbM/JH/nkijl4tNLxpSw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "filter", "value": "all_ungraded_todo_items"}, {"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "start_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-10-05T18%3A30%3A00.000Z"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1379, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:19 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4d7864f52c3e1044c9fc0403d0792af0\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&end_date=2025-10-05T18%3A30%3A00.000Z&filter=all_ungraded_todo_items&start_date=2025-08-31T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&end_date=2025-10-05T18%3A30%3A00.000Z&filter=all_ungraded_todo_items&start_date=2025-08-31T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&end_date=2025-10-05T18%3A30%3A00.000Z&filter=all_ungraded_todo_items&start_date=2025-08-31T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=5.95, cache_fetch_hit.active_support;dur=0.04, cache_read_multi.active_support;dur=0.30, start_processing.action_controller;dur=0.01, sql.active_record;dur=4.79, instance.active_record;dur=0.03, instantiation.active_record;dur=1.57, process_action.action_controller;dur=26.12"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner;n=index;b=1641700;m=1641700;u=0.02;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "503.730195155376"}, {"name": "x-request-context-id", "value": "81d6344c-5137-4dd8-b833-fc380084c42c"}, {"name": "x-request-cost", "value": "0.024514790973201528"}, {"name": "x-runtime", "value": "0.352106"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2187, "bodySize": 12, "_transferSize": 2199, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:18.413Z", "time": 808.01100001554, "timings": {"blocked": 446.3200000137892, "dns": -1, "ssl": -1, "connect": -1, "send": 0.1209999999999809, "wait": 358.1500000081118, "receive": 3.419999993639067, "_blocked_queueing": 6.381000013789162, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871548", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "5s/m4kxxkzMBIq/RQ+x90v9I3UhXbzlnW7yGwlScFSW0+ITTIgvwS1Bznr0howyxxnueDgdEUx1w5tKgF9pPcw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "include%5B%5D", "value": "web_conference"}, {"name": "include%5B%5D", "value": "series_head"}, {"name": "include%5B%5D", "value": "series_natural_language"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1435, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:39 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&undated=1&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&undated=1&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&undated=1&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=12.51, cache_fetch_hit.active_support;dur=0.11, cache_read_multi.active_support;dur=0.37, start_processing.action_controller;dur=0.02, sql.active_record;dur=13.70, instance.active_record;dur=0.18, instantiation.active_record;dur=10.99, cache_generate.active_support;dur=6.52, cache_write.active_support;dur=3.34, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.89, process_action.action_controller;dur=70.68"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1803664;m=1803664;u=0.06;y=0.00;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "450.57483150503003"}, {"name": "x-request-context-id", "value": "57981c68-72bf-4cbd-b8b0-5f79fd76fa78"}, {"name": "x-request-cost", "value": "0.06557312400553883"}, {"name": "x-runtime", "value": "0.769188"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2515, "bodySize": 12, "_transferSize": 2527, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:38.549Z", "time": 784.4120000081602, "timings": {"blocked": 5.782999998299405, "dns": -1, "ssl": -1, "connect": -1, "send": 0.08499999999999996, "wait": 777.7539999898001, "receive": 0.7900000200606883, "_blocked_queueing": 1.348999998299405, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871391", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&type=assignment&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "5s/m4kxxkzMBIq/RQ+x90v9I3UhXbzlnW7yGwlScFSW0+ITTIgvwS1Bznr0howyxxnueDgdEUx1w5tKgF9pPcw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "type", "value": "assignment"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1358, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:39 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"7e9309292be2db0d4bfd17fa29256b5c\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=assignment&undated=1&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=assignment&undated=1&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=assignment&undated=1&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=18.38, cache_fetch_hit.active_support;dur=0.08, cache_read_multi.active_support;dur=0.77, start_processing.action_controller;dur=0.04, sql.active_record;dur=32.22, instance.active_record;dur=0.40, instantiation.active_record;dur=15.06, cache_generate.active_support;dur=6.29, cache_write.active_support;dur=2.18, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.70, process_action.action_controller;dur=156.19"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1618476;m=1618476;u=0.14;y=0.00;d=0.02;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "600.0"}, {"name": "x-request-context-id", "value": "15784b99-045a-4e05-a2cd-59db152bba5d"}, {"name": "x-request-cost", "value": "0.1570488300008792"}, {"name": "x-runtime", "value": "0.845804"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 5116, "mimeType": "application/json", "compression": -13, "text": "[{\"title\":\"assignment1 Undated\",\"description\":\"\",\"submission_types\":\"online_text_entry,online_upload\",\"workflow_state\":\"published\",\"created_at\":\"2025-09-03T04:54:42Z\",\"updated_at\":\"2025-09-03T04:54:55Z\",\"all_day\":false,\"all_day_date\":null,\"id\":\"assignment_5\",\"type\":\"assignment\",\"assignment\":{\"id\":\"5\",\"description\":\"\",\"due_at\":null,\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"1\",\"grading_standard_id\":null,\"created_at\":\"2025-09-03T04:54:42Z\",\"updated_at\":\"2025-09-03T04:54:55Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":3,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6ImFkZjIzNDkwLWFiMzQtNDA2ZS1iYTAxLTUwYWZmNzVkOWRlMiJ9.krOaXOrVNiT_kelVWEKQyJ2EeSZ6Nh9HDneKpZevJEM\",\"lti_context_id\":\"adf23490-ab34-406e-ba01-50aff75d9de2\",\"course_id\":\"2\",\"name\":\"assignment1 Undated\",\"submission_types\":[\"online_text_entry\",\"online_upload\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":\"2\",\"original_assignment_id\":\"3\",\"original_lti_resource_link_id\":null,\"original_assignment_name\":\"assignment1\",\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/2/assignments/5\",\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/2/assignments/5/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/2/assignments/5\",\"context_code\":\"course_2\",\"context_name\":\"Course 1\",\"context_color\":null,\"end_at\":null,\"start_at\":null,\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_5\",\"important_dates\":false},{\"title\":\"assignment1 Undated\",\"description\":null,\"submission_types\":\"online_text_entry,online_upload\",\"workflow_state\":\"published\",\"created_at\":\"2025-09-04T07:07:16Z\",\"updated_at\":\"2025-09-04T07:07:29Z\",\"all_day\":false,\"all_day_date\":null,\"id\":\"assignment_10\",\"type\":\"assignment\",\"assignment\":{\"id\":\"10\",\"description\":null,\"due_at\":null,\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"2\",\"grading_standard_id\":null,\"created_at\":\"2025-09-04T07:07:16Z\",\"updated_at\":\"2025-09-04T07:07:29Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":6,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjM3ZTc0YTYxLTE0OTUtNDIyOC04NjYwLWViNzY2MWUxOTk4YSJ9.0wA4EeZycg6IyKnxTHybbFW0ryc8LiUp9HWNuT6U6Qs\",\"lti_context_id\":\"37e74a61-1495-4228-8660-eb7661e1998a\",\"course_id\":\"1\",\"name\":\"assignment1 Undated\",\"submission_types\":[\"online_text_entry\",\"online_upload\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":\"1\",\"original_assignment_id\":\"6\",\"original_lti_resource_link_id\":null,\"original_assignment_name\":\"assignment1 Group\",\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/1/assignments/10\",\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/1/assignments/10/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/1/assignments/10\",\"context_code\":\"course_1\",\"context_name\":\"Shard1 Course\",\"context_color\":null,\"end_at\":null,\"start_at\":null,\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_10\",\"important_dates\":false}]"}, "redirectURL": "", "headersSize": 2273, "bodySize": 5129, "_transferSize": 7402, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:38.549Z", "time": 871.0579999897163, "timings": {"blocked": 6.8659999876637015, "dns": -1, "ssl": -1, "connect": -1, "send": 0.06799999999999962, "wait": 862.9189999942947, "receive": 1.205000007757917, "_blocked_queueing": 1.5539999876637012, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871537", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&type=sub_assignment&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "5s/m4kxxkzMBIq/RQ+x90v9I3UhXbzlnW7yGwlScFSW0+ITTIgvwS1Bznr0howyxxnueDgdEUx1w5tKgF9pPcw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "type", "value": "sub_assignment"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1362, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:39 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=sub_assignment&undated=1&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=sub_assignment&undated=1&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=sub_assignment&undated=1&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=14.99, cache_fetch_hit.active_support;dur=0.09, cache_read_multi.active_support;dur=0.34, start_processing.action_controller;dur=0.02, sql.active_record;dur=14.72, instance.active_record;dur=0.17, instantiation.active_record;dur=8.32, cache_generate.active_support;dur=6.83, cache_write.active_support;dur=2.07, start_transaction.active_record;dur=0.00, transaction.active_record;dur=2.98, process_action.action_controller;dur=76.37"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1672432;m=1672432;u=0.07;y=0.00;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "500.547052754783"}, {"name": "x-request-context-id", "value": "1a698b6c-caa7-44a2-aa3e-dfd08bbef5a9"}, {"name": "x-request-cost", "value": "0.07896483499364848"}, {"name": "x-runtime", "value": "0.772620"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2291, "bodySize": 12, "_transferSize": 2303, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:38.549Z", "time": 788.7420000042766, "timings": {"blocked": 5.619000017298386, "dns": -1, "ssl": -1, "connect": -1, "send": 0.21399999999999952, "wait": 782.1759999934901, "receive": 0.7329999934881926, "_blocked_queueing": 1.7490000172983855, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871751", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner_notes?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "5s/m4kxxkzMBIq/RQ+x90v9I3UhXbzlnW7yGwlScFSW0+ITTIgvwS1Bznr0howyxxnueDgdEUx1w5tKgF9pPcw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1340, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:39 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"97f719721b343212692dcb5b3c82e380\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=6.42, cache_fetch_hit.active_support;dur=0.07, cache_read_multi.active_support;dur=0.37, start_processing.action_controller;dur=0.03, sql.active_record;dur=5.24, instance.active_record;dur=0.08, instantiation.active_record;dur=4.21, cache_generate.active_support;dur=6.21, cache_write.active_support;dur=2.50, process_action.action_controller;dur=41.74"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner_notes;n=index;b=1642148;m=1642276;u=0.04;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "350.3984096316"}, {"name": "x-request-context-id", "value": "7a15e012-e4d4-4dfd-bfa2-b5a38a641f88"}, {"name": "x-request-cost", "value": "0.039498541005803744"}, {"name": "x-runtime", "value": "0.730407"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 424, "mimeType": "application/json", "compression": -12, "text": "[{\"id\":\"1\",\"todo_date\":\"2025-09-04T18:29:00Z\",\"title\":\"TEST TODO\",\"details\":\"\",\"user_id\":\"4\",\"course_id\":null,\"workflow_state\":\"active\",\"created_at\":\"2025-09-04T07:18:26Z\",\"updated_at\":\"2025-09-04T07:18:26Z\"},{\"id\":\"2\",\"todo_date\":\"2025-09-05T18:29:00Z\",\"title\":\"TEST TODO Undated\",\"details\":\"\",\"user_id\":\"4\",\"course_id\":\"2\",\"workflow_state\":\"active\",\"created_at\":\"2025-09-04T07:18:42Z\",\"updated_at\":\"2025-09-04T07:18:42Z\"}]"}, "redirectURL": "", "headersSize": 1608, "bodySize": 436, "_transferSize": 2044, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:38.549Z", "time": 754.98399999924, "timings": {"blocked": 5.934999973829836, "dns": -1, "ssl": -1, "connect": -1, "send": 0.16800000000000015, "wait": 747.6609999907464, "receive": 1.2200000346638262, "_blocked_queueing": 1.8999999738298357, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871272", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner/items?filter=ungraded_todo_items&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "5s/m4kxxkzMBIq/RQ+x90v9I3UhXbzlnW7yGwlScFSW0+ITTIgvwS1Bznr0howyxxnueDgdEUx1w5tKgF9pPcw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "filter", "value": "ungraded_todo_items"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1340, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:39 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"afbe3919de802386c8b493f672c4f62f\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&filter=ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&filter=ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&filter=ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=7.94, cache_fetch_hit.active_support;dur=0.07, cache_read_multi.active_support;dur=0.43, start_processing.action_controller;dur=0.03, sql.active_record;dur=28.89, instance.active_record;dur=0.13, instantiation.active_record;dur=6.71, cache_generate.active_support;dur=97.59, cache_write.active_support;dur=5.04, process_action.action_controller;dur=111.70"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner;n=index;b=1650152;m=1650152;u=0.08;y=0.01;d=0.02;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "550.804152587557"}, {"name": "x-request-context-id", "value": "003f77ef-182f-45f3-90ae-4724ef1e6752"}, {"name": "x-request-cost", "value": "0.10231379103521476"}, {"name": "x-runtime", "value": "0.805934"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 917, "mimeType": "application/json", "compression": -12, "text": "[{\"context_type\":\"Course\",\"course_id\":\"1\",\"plannable_id\":\"3\",\"planner_override\":null,\"plannable_type\":\"announcement\",\"new_activity\":true,\"submissions\":false,\"plannable_date\":\"2025-09-04T07:12:14Z\",\"plannable\":{\"id\":\"3\",\"title\":\"Shard 1 Announcement\",\"unread_count\":0,\"read_state\":\"unread\",\"created_at\":\"2025-09-04T07:12:14Z\",\"updated_at\":\"2025-09-04T07:12:31Z\"},\"html_url\":\"/courses/1/discussion_topics/3\",\"context_name\":\"Shard1 Course\",\"context_image\":null},{\"context_type\":\"Course\",\"course_id\":\"1\",\"plannable_id\":\"4\",\"planner_override\":null,\"plannable_type\":\"announcement\",\"new_activity\":true,\"submissions\":false,\"plannable_date\":\"2025-09-04T07:13:34Z\",\"plannable\":{\"id\":\"4\",\"title\":\"Shard 2 Announcement\",\"unread_count\":0,\"read_state\":\"unread\",\"created_at\":\"2025-09-04T07:13:34Z\",\"updated_at\":\"2025-09-04T07:13:34Z\"},\"html_url\":\"/courses/1/discussion_topics/4\",\"context_name\":\"Shard1 Course\",\"context_image\":null}]"}, "redirectURL": "", "headersSize": 2139, "bodySize": 929, "_transferSize": 3068, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:38.549Z", "time": 824.7160000028089, "timings": {"blocked": 6.119999978912994, "dns": -1, "ssl": -1, "connect": -1, "send": 0.07399999999999984, "wait": 817.7679999869187, "receive": 0.7540000369772315, "_blocked_queueing": 1.9579999789129943, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1871746", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "25283", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "25328", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "26505", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "27849", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "27850", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "25327", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "pageref": "page_1", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner/items?filter=all_ungraded_todo_items&context_codes%5B%5D=user_4&undated=1&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "5s/m4kxxkzMBIq/RQ+x90v9I3UhXbzlnW7yGwlScFSW0+ITTIgvwS1Bznr0howyxxnueDgdEUx1w5tKgF9pPcw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "filter", "value": "all_ungraded_todo_items"}, {"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "undated", "value": "1"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1313, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Thu, 04 Sep 2025 07:23:39 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"d901324d3341835ad360b4c37a07ea32\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&filter=all_ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&filter=all_ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&filter=all_ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=6.47, cache_fetch_hit.active_support;dur=0.07, cache_read_multi.active_support;dur=0.38, start_processing.action_controller;dur=0.03, sql.active_record;dur=9.68, instance.active_record;dur=0.06, instantiation.active_record;dur=3.39, cache_generate.active_support;dur=14.47, cache_write.active_support;dur=2.36, process_action.action_controller;dur=49.21"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner;n=index;b=1639596;m=1639596;u=0.05;y=0.00;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "400.42265932493"}, {"name": "x-request-context-id", "value": "78f4d887-4797-4a6b-bee6-80eee9b99c71"}, {"name": "x-request-cost", "value": "0.05196795804178489"}, {"name": "x-runtime", "value": "0.739285"}, {"name": "x-session-id", "value": "1df29eb7a1a5d1f18ea23fd324d4231f"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2053, "bodySize": 12, "_transferSize": 2065, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-04T07:23:38.549Z", "time": 762.6619999937247, "timings": {"blocked": 6.15499998890236, "dns": -1, "ssl": -1, "connect": -1, "send": 0.0680000000000005, "wait": 754.7709999931175, "receive": 1.6680000117048621, "_blocked_queueing": 2.00899998890236, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}