#! /usr/bin/env groovy
pipeline {
    agent { label 'docker' }

    environment {
        TEST_IMAGE_NAME = "stride-calendar-viewer"
        PUBLISHED_IMAGE_TAG = "starlord.inscloudgate.net/jenkins/stride-calendar-viewer:${GERRIT_PATCHSET_REVISION}"
        CHANGE_OWNER = "${GERRIT_CHANGE_OWNER_EMAIL.split("@")[0]}"
    }


    stages {
        stage("Build Image") {
            steps {
                dockerCacheLoad(image: env.TEST_IMAGE_NAME)
                timeout(time: 30, unit: 'MINUTES') {
                    sh """docker build --pull --tag "$TEST_IMAGE_NAME" --tag "$PUBLISHED_IMAGE_TAG"  -f Dockerfile ."""
                }
            }
        }
        stage("Test") {
            steps {
                sh "./build.sh"
            }
        }
        stage("Gerrit Push Publish") {
            parallel {
                stage("Production image") {
                    steps {
                        timeout(time: 30, unit: 'MINUTES') {
                            sh """docker push $PUBLISHED_IMAGE_TAG"""
                        }
                    }
                }
                stage("Docker build cache") {
                    steps {
                        timeout(time: 5, unit: 'MINUTES') {
                            dockerCacheStore(image: env.TEST_IMAGE_NAME)
                        }
                    }
                }
            }
        }
    }

    post {
        failure{
            script {
                slackSend failOnError: true, channel: '#customdev-builds', color: 'danger', message: "@${CHANGE_OWNER} the stride-calendar-viewer gerrit-push <${env.BUILD_URL}|build> for your recently pushed patch failed!" 
            }
        }
        success{
            script {
                slackSend failOnError: true, channel: '#customdev-builds', color: 'good', message: "stride-calendar-viewer gerrit-push <${env.BUILD_URL}|build> pushed a new images!\n$PUBLISHED_IMAGE_TAG"
            }
        }
    }
}
