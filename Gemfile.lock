GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activerecord-import (2.2.0)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aes_key_wrap (1.1.0)
    ast (2.4.3)
    attr_encrypted (4.0.0)
      encryptor (~> 3.0.0)
    aws-eventstream (1.4.0)
    aws-partitions (1.1146.0)
    aws-sdk-core (3.229.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      bigdecimal
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.110.0)
      aws-sdk-core (~> 3, >= 3.228.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.196.1)
      aws-sdk-core (~> 3, >= 3.228.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    base64 (0.3.0)
    bearcat (1.5.39)
      activesupport
      connection_pool (>= 2.2.0, < 3.0)
      footrest (>= 0.2.2)
    bigdecimal (3.2.2)
    bindata (2.5.1)
    bindex (0.8.1)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.1.0)
      racc
    browser (2.5.0)
    builder (3.3.0)
    byebug (12.0.0)
    cancancan (3.6.1)
    canvas_sync (0.22.18)
      activejob
      activerecord-import
      bearcat (>= 1.5.39)
      chronic_duration
      json-jwt
      open-uri
      rails (>= 4)
      redis (>= 4.2)
      rubyzip
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    chronic_duration (0.10.6)
      numerizer (~> 0.1.1)
    coderay (1.1.3)
    concurrent-ruby (1.3.4)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.5)
    daemons (1.4.1)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    diff-lcs (1.6.2)
    encryptor (3.0.0)
    erb (5.0.2)
    erubi (1.13.1)
    et-orbi (1.3.0)
      tzinfo
    eventmachine (1.2.7)
    execjs (2.10.0)
    factory_bot (6.5.4)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.0)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faker (3.5.2)
      i18n (>= 1.8.11, < 2)
    faraday (1.10.4)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.1)
    faraday-excon (1.1.0)
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    footrest (0.5.8)
      activesupport (>= 3.0.0)
      faraday (>= 0.9.0, < 2)
      link_header (>= 0.0.7)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gergich (2.2.1)
      httparty (~> 0.17)
      sqlite3 (>= 1.4, < 3.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashdiff (1.2.0)
    hashie (5.0.0)
    health_check (3.1.0)
      railties (>= 5.0)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.9.0)
      mutex_m
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.2.2)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    ims-lti (1.2.9)
      builder (>= 1.0, < 4.0)
      oauth (>= 0.4.5)
      rexml
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.14.1)
      actionview (>= 7.0.0)
      activesupport (>= 7.0.0)
    jmespath (1.6.2)
    json (2.13.2)
    json-jwt (1.15.3.1)
      activesupport (>= 4.2)
      aes_key_wrap
      bindata
      httpclient
    jwt (3.1.2)
      base64
    language_server-protocol (********)
    link_header (0.0.8)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lti_roles (0.0.4)
      activesupport
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.3)
    method_source (1.1.0)
    mini_mime (1.1.5)
    minitest (5.25.5)
    miscellany (0.1.26)
      rails (>= 5, < 8.0)
    msgpack (1.8.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.2.0)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    numerizer (0.1.1)
    oauth (1.1.0)
      oauth-tty (~> 1.0, >= 1.0.1)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oauth-tty (1.0.5)
      version_gem (~> 1.1, >= 1.1.1)
    open-uri (0.5.0)
      stringio
      time
      uri
    package_json (0.1.0)
    panda_pal (5.13.4)
      attr_encrypted (~> 4.0.0)
      browser (= 2.5.0)
      httparty
      ims-lti (~> 1.2.4)
      json-jwt
      jwt
      rails (>= 4.2)
      ros-apartment (~> 3.0)
      secure_headers (~> 6.1)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    paul_bunyan (2.1.0)
      request_store
    pg (1.6.1-arm64-darwin)
    pg (1.6.1-x86_64-linux)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.1)
    puma (5.6.9)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-proxy (0.7.7)
      rack
    rack-test (2.2.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    react-rails (3.2.1)
      babel-transpiler (>= 0.7.0)
      connection_pool
      execjs
      railties (>= 3.2)
      tilt
    react_on_rails (14.2.0)
      addressable
      connection_pool
      execjs (~> 2.5)
      rails (>= 5.2)
      rainbow (~> 3.0)
    redis (5.1.0)
      redis-client (>= 0.17.0)
    redis-client (0.25.2)
      connection_pool
    regexp_parser (2.11.2)
    reline (0.6.2)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    rexml (3.4.1)
    ros-apartment (3.2.0)
      activerecord (>= 6.1.0, < 8.1)
      activesupport (>= 6.1.0, < 8.1)
      parallel (< 2.0)
      public_suffix (>= 2.0.5, <= 6.0.1)
      rack (>= 1.3.6, < 4.0)
    ros-apartment-sidekiq (1.2.0)
      ros-apartment (>= 1.0)
      sidekiq (>= 2.11)
    rspec-core (3.13.5)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rubocop (1.79.2)
      json (~> 2.3)
      language_server-protocol (~> ********)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.46.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.46.0)
      parser (>= *******)
      prism (~> 1.4)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    rufus-scheduler (3.9.2)
      fugit (~> 1.1, >= 1.11.1)
    secure_headers (6.7.0)
    selenium-webdriver (4.35.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 4.0)
      websocket (~> 1.0)
    semantic_range (3.1.0)
    sentry-rails (5.26.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.26.0)
    sentry-ruby (5.26.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sentry-sidekiq (5.26.0)
      sentry-ruby (~> 5.26.0)
      sidekiq (>= 3.0)
    shakapacker (8.3.0)
      activesupport (>= 5.2)
      package_json
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    shoulda (2.11.3)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-failures (1.0.4)
      sidekiq (>= 4.0.0)
    sidekiq-scheduler (5.0.6)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 6, < 8)
      tilt (>= 1.4.0, < 3)
    snaky_hash (2.0.3)
      hashie (>= 0.1.0, < 6)
      version_gem (>= 1.1.8, < 3)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sqlite3 (2.7.3-arm64-darwin)
    sqlite3 (2.7.3-x86_64-linux-gnu)
    stackprof (0.2.27)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    thin (2.0.1)
      daemons (~> 1.0, >= 1.0.9)
      eventmachine (~> 1.0, >= 1.0.4)
      logger
      rack (>= 1, < 4)
    thor (1.4.0)
    tilt (2.6.1)
    time (0.4.1)
      date
    timecop (0.9.10)
    timeout (0.4.3)
    turbo-rails (2.0.12)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2025.2)
      tzinfo (>= 1.0.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    version_gem (1.1.8)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)

PLATFORMS
  arm64-darwin-23
  arm64-darwin-24
  x86_64-linux

DEPENDENCIES
  aws-sdk-s3
  bearcat (~> 1.5.31)
  bootsnap (>= 1.4.4)
  brakeman
  byebug
  cancancan (~> 3.5)
  canvas_sync (~> 0.22.18)
  capybara
  debug
  factory_bot_rails
  faker
  faraday-follow_redirects (~> 0.3.0)
  gergich
  health_check
  importmap-rails
  jbuilder
  lti_roles (~> 0.0.4)
  miscellany (~> 0.1.22)
  mutex_m (~> 0.2.0)
  panda_pal (~> 5.13)
  paul_bunyan
  pg (~> 1.5)
  pry
  puma (~> 5.0)
  rack-cors
  rails (~> 7.0.8, >= *******)
  react-rails (~> 3.2)
  react_on_rails (= 14.2)
  redis (~> 5.1.0)
  ros-apartment-sidekiq (~> 1.2)
  rspec-rails
  rubocop (~> 1.67)
  rubyzip (= 2.3.2)
  selenium-webdriver
  sentry-rails
  sentry-ruby
  sentry-sidekiq
  shakapacker (= 8.3)
  shoulda
  shoulda-matchers
  sidekiq (~> 7.2)
  sidekiq-failures
  sidekiq-scheduler (~> 5.0)
  sprockets-rails
  stackprof
  stimulus-rails
  thin (~> 2.0)
  timecop
  turbo-rails
  tzinfo-data
  web-console
  webmock

RUBY VERSION
   ruby 3.3.3p89

BUNDLED WITH
   2.7.1
