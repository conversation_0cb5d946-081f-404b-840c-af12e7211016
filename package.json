{"name": "stride-calendar-viewer", "packageManager": "yarn@1.22.22", "private": true, "engines": {"node": "22.x"}, "dependencies": {"@babel/core": "7", "@babel/plugin-transform-runtime": "7", "@babel/preset-env": "7", "@babel/preset-react": "^7.25.9", "@babel/runtime": "7", "@inst_proserv/toolkit": "^0.2.15", "@instructure/ui": "^10.24.2", "@instructure/ui-alerts": "^10.24.2", "@instructure/ui-badge": "^10.24.2", "@instructure/ui-buttons": "^10.24.2", "@instructure/ui-calendar": "^10.24.2", "@instructure/ui-drilldown": "^10.24.2", "@instructure/ui-flex": "^10.24.2", "@instructure/ui-grid": "^10.24.2", "@instructure/ui-heading": "^10.24.2", "@instructure/ui-icons": "^10.24.2", "@instructure/ui-img": "^10.24.2", "@instructure/ui-modal": "^10.24.2", "@instructure/ui-pagination": "^10.24.2", "@instructure/ui-responsive": "^10.24.2", "@instructure/ui-spinner": "^10.24.2", "@instructure/ui-table": "^10.24.2", "@instructure/ui-text": "^10.24.2", "@instructure/ui-tooltip": "^10.24.2", "@instructure/ui-truncate-text": "^10.24.2", "@instructure/ui-view": "^10.24.2", "@types/babel__core": "7", "@types/webpack": "5", "axios": "^1.7.7", "babel-loader": "8", "compression-webpack-plugin": "9", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "luxon": "^3.6.1", "mini-css-extract-plugin": "^2.9.1", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "prettier": "^3.6.2", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-on-rails": "14.2.0", "react-router": "^6.28.0", "react-router-dom": "^6.28.0", "react_ujs": "^3.2.1", "sass": "^1.90.0", "sass-loader": "^16.0.5", "shakapacker": "^8.3.0", "style-loader": "^4.0.0", "terser-webpack-plugin": "5", "webpack": "5", "webpack-assets-manifest": "5", "webpack-cli": "4", "webpack-merge": "5"}, "version": "0.1.0", "babel": {"presets": ["./node_modules/shakapacker/package/babel/preset.js", "@babel/preset-react"]}, "browserslist": ["defaults"], "devDependencies": {"webpack-dev-server": "4"}}