version: '2'

services:
  web:
    image: "${TEST_IMAGE_NAME}"
    build:
      context: .
      dockerfile: ./Dockerfile
    environment:
      APP_DOMAIN: web
      GERGICH_KEY: "${GERGICH_KEY}"
      GERRIT_PROJECT: "${GERRIT_PROJECT}"
      GERRIT_HOST: "${GERRIT_HOST}"
      GERRIT_BRANCH: "${GERRIT_BRANCH}"
      DATABASE_USERNAME: postgres
      DATABASE_PASSWORD: stride_calendar_viewer_postgres_password
      DATABASE_ADDRESS: postgres
      RAILS_ENV: test
      REDIS_URL: redis://redis:6379
    links:
      - postgres
      - redis
    volumes:
      - '.git:/usr/src/app/.git'

  postgres:
    image: postgres:16.4
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: stride_calendar_viewer_postgres_password
      POSTGRES_HOST_AUTH_METHOD: trust

  redis:
    image: redis

volumes:
  coverage: {}
