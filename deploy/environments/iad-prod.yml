app:
  name: stride-calendar-viewer
  environment: production
  vpc_cluster: insproserv-prod
  region: us-east-1

  tags:
    environment: staging

  load_balancers:
    web:
      ssl_cert_arn: 'arn:aws:acm:us-east-1:376567524831:certificate/6c8d5406-0caf-49e1-b615-b9daf888283e'
      ssl_policy: 'ELBSecurityPolicy-TLS13-1-2-2021-06'

  sso_roles:
    - ProServe-Engineers
    - ProServe-Admins

  env_vars:
    SENTRY_CURRENT_ENV: production
    RAILS_MAX_THREADS: "20"

  instance_pools:
    web:
      instance_type: t3.medium
      http_port: 8080
      https_port: 8443
      health_check_path: "/health_check"
      min_size: 2
      max_size: 8
      env_vars:
        LEGACY_PORTS: '0'
        RAILS_ENV: 'production'
      tags:
        role: web
    work:
      instance_type: t3.medium
      min_size: 2
      max_size: 6
      env_vars:
        RAILS_ENV: 'production'
      hooks:
        post:
          - command: bundle exec rake db:migrate
      tags:
        role: work

  databases:
    stridecalendarviewerpostgres:
      db_instance_class: db.m5.large
      storage_type: gp2
      allocated_storage: 200

    shardalpha:
      db_instance_class: db.m5.large
      storage_type: gp2
      allocated_storage: 150
      env_var_prefix: SHARD_DB_ALPHA
      master_username: stridecalendarvieweruser
      engine_version: '16.4'

    shardbravo:
      db_instance_class: db.m5.large
      storage_type: gp2
      allocated_storage: 150
      env_var_prefix: SHARD_DB_BRAVO
      master_username: stridecalendarvieweruser
      engine_version: '16.4'

  caches:
    redis:
      cache_node_type: cache.t3.medium
      env_var_prefix: REDIS
      num_cache_nodes: 1
      engine_version: '7.0'
