app:
  name: stride-calendar-viewer
  environment: test
  vpc_cluster: insproserv-nonprod
  region: us-east-1

  tags:
    environment: test

  load_balancers:
    web:
      ssl_cert_arn: 'arn:aws:acm:us-east-1:376567524831:certificate/6c8d5406-0caf-49e1-b615-b9daf888283e'

  sso_roles:
    - ProServe-Engineers
    - ProServe-Admins

  env_vars:
    SENTRY_CURRENT_ENV: UAT

  instance_pools:
    web:
      instance_type: t3.medium
      http_port: 8080
      https_port: 8443
      health_check_path: "/health_check"
      min_size: 1
      max_size: 2
      env_vars:
        LEGACY_PORTS: '0'
        RAILS_ENV: 'production'
      tags:
        role: web
    work:
      instance_type: t3.medium
      min_size: 1
      max_size: 1
      env_vars:
        RAILS_ENV: 'production'
      hooks:
        post:
          - command: bundle exec rake db:migrate
      tags:
        role: work

  databases:
    stridecalendarviewerpostgres:
      db_instance_class: db.t3.medium
      storage_type: gp2
      allocated_storage: 20
      engine_version: '16.8'

    shardalpha:
      db_instance_class: db.t3.medium
      storage_type: gp2
      allocated_storage: 15
      env_var_prefix: SHARD_DB_ALPHA
      master_username: stridecalendarvieweruser
      engine_version: '16.8'

    shardbravo:
      db_instance_class: db.t3.medium
      storage_type: gp2
      allocated_storage: 15
      env_var_prefix: SHARD_DB_BRAVO
      master_username: stridecalendarvieweruser
      engine_version: '16.8'

  caches:
    redis:
      cache_node_type: cache.t3.medium
      env_var_prefix: REDIS
      num_cache_nodes: 1
      engine_version: '7.0'
