app:
  name: stride-calendar-viewer
  environment: qa
  vpc_cluster: inseng-dev
  region: us-west-2

  tags:
    environment: qa

  load_balancers:
    web:
      ssl_cert_arn: "arn:aws:acm:us-west-2:830473435438:certificate/b119c8b6-9305-4532-9798-dcfbf712158b"

  sso_roles:
    - ProServe-Admins
    - ProServe-Engineers

  env_vars:
    SENTRY_CURRENT_ENV: QA

  instance_pools:
    web:
      instance_type: t2.medium
      http_port: 8080
      https_port: 8443
      health_check_path: "/health_check"
      min_size: 1
      max_size: 2
      env_vars:
        LEGACY_PORTS: '0'
        RAILS_ENV: 'production'
      tags:
        role: web
    work:
      instance_type: t2.medium
      min_size: 1
      max_size: 1
      env_vars:
        RAILS_ENV: 'production'
      hooks:
        post:
          - command: bundle exec rake db:migrate
      tags:
        role: work

  databases:
    stridecalendarviewerpostgres:
      db_instance_class: db.t3.medium
      storage_type: gp2
      allocated_storage: 10

  caches:
    redis:
      cache_node_type: cache.t2.medium
      env_var_prefix: REDIS
      num_cache_nodes: 1
      engine_version: '7.0'
