# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateRoles < ActiveRecord::Migration[5.1]
  def change
    create_table :roles do |t|
      t.bigint :canvas_id, null: false
      t.string :label
      t.string :base_role_type
      t.bigint :canvas_account_id
      t.string :workflow_state
      t.json :permissions

      t.timestamps
    end
    add_index :roles, :canvas_id, unique: true
    add_index :roles, :canvas_account_id
  end
end
