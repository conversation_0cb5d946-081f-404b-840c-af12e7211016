class CreateStudentObserverLinks < ActiveRecord::Migration[7.0]
  def change
    create_table :student_observer_links do |t|
      t.bigint :observer_user_id, null: false
      t.bigint :observed_student_id, null: false
      t.datetime :expires_at, null: false
      t.datetime :renewed_at
      t.string :status, null: false, default: 'active'


      t.timestamps
    end

    add_index :student_observer_links, :observer_user_id
    add_index :student_observer_links, :observed_student_id
    add_index :student_observer_links, :expires_at
    add_index :student_observer_links, :status
    add_index :student_observer_links, [:observer_user_id, :status]
    add_index :student_observer_links, [:observed_student_id, :status]
  end
end
