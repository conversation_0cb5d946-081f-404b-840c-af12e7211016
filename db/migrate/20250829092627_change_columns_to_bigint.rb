class ChangeColumnsToBigint < ActiveRecord::Migration[7.0]
  def change
    reversible do |dir|
      dir.up   do
        change_column :enrollments, :canvas_role_id, :bigint
        change_column :user_shard_associations, :canvas_user_id, :bigint
        change_column :user_shard_associations, :canvas_user_id, :bigint
        change_column :terms, :canvas_id, :bigint
        change_column :courses, :canvas_account_id, :bigint
        change_column :courses, :canvas_term_id, :bigint
      end
      dir.down do
        change_column :courses, :canvas_term_id, :integer
        change_column :courses, :canvas_account_id, :integer
        change_column :terms, :canvas_id, :integer
        change_column :user_shard_associations, :canvas_user_id, :integer
        change_column :enrollments, :canvas_role_id, :integer
      end
    end
  end
end
