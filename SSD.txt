Student Observation and Calendar Viewer Application






Student Observation and Calendar Viewer Application


User Stories
User stories briefly and simply describe the Solution’s users, what features they need, and why. They are the foundation for the Solution’s requirements, milestones, and test cases.
1
	As a user, I can access a user interface so that I can interact with data.
	2
	As a Course Level Administrator Role, I can create a temporary Canvas account observer link with one of my students, so that I can view the selected student’s courses, course content, grades, and submissions in order to assist the student.
	3
	As an Account Level Role, I can create a temporary Canvas account observer link with a student in my sub-account, so that I can view the selected student’s courses, course content, grades, and submissions in order to assist the student.
	4
	As a Solution User, My temporary Observer Account Links and Student Calendar Access with the selected student are removed after one hour, so that I don’t have to remove the links myself when I am finished with my observations.
	5
	As a Course Level Administrator Role, I can access a view of the selected student’s Canvas calendar, so that I can better understand their workload when assigning content or when attempting to meet with them.
	6
	As an Account Level Role, I can access a view of the selected student’s Canvas calendar, so that I can better understand their workload when assigning content or when attempting to meet with them.



________________
SCHEDULE 1
Milestones and Deliverables
Milestone 1: Design Solution
USER STORIES INCLUDED
	1. As a user, I can access a user interface so that I can interact with data.
	SUMMARY
	Creation of and Customer approval for the finalized user interface design mockups for the Student Observation and Calendar Viewer Application.


Note: Solution designs proposed and accepted in this milestone supersede any other mockups, including wireframes in this SSD.
company Requirements
1. Provide a UI/UX design resource.
2. Create visual mock-ups for the deliverables identified above which reflect the requirements in the remaining milestones.
3. Present mock-ups for review and iterate once (if necessary) to integrate customer feedback.
Customer Requirements
1. Provide a primary stakeholder to review and approve mockup designs.
2. Provide any feedback needed to complete the mockup.
3. UAT consists of review and approval of final draft of UI mockup for the Student Observation and Calendar Viewer Application interface.
________________
Milestone 2: Temporary Student Observer Account Links
USER STORIES INCLUDED
	2. As a Course Level Administrator Role, I can create a temporary Canvas account observer link with one of my students, so that I can view the selected student’s courses, course content, grades, and submissions in order to assist the student.
3. As an Account Level Role, I can create a temporary Canvas account observer link with a student in my sub-account, so that I can view the selected student’s courses, course content, grades, and submissions in order to assist the student.
4. As a Solution User, My temporary Observer Account Links and Student Calendar Access with the selected student are removed after one hour, so that I don’t have to remove the links myself when I am finished with my observations.
	SUMMARY
	An application will be created that allows for users to access a list of students at the course or sub-account level, select a student, and initiate a temporary account-linked observer/observee pairing.  Users can end the link whenever they desire and links will automatically dissolve after one hour.
Course Level Admin Users access via a course launch, and are presented with their course’s students.  A single student can be selected and a temporary account-level observer/observee link will be established.
Account Level Users access via a sub-account launch, and are presented with their sub-account’s students.  A single student can be selected and a temporary account-level observer/observee link will be established.


company Requirements
1. Complete necessary pre-development tasks.
2. Implement an application that can be used as an external tool in Canvas.
   1. Allow users to access and launch the application as detailed in
SCHEDULE 2 - ‘User Access’.
   2. Set up a Regular Update Sync Process for the Solution to pull Sub-Account, Student Enrollment, and Course data every six hours.
   1. When accessed by a Solution User, up-to-date Canvas data will be synced so that the accessing user has access to live data.
   3. Install the Application at the Parent Consortium Instance: https://LEARNZ.company.com/, as well as at the individual Canvas Production Child Instances:
   1. https://LEARNZ1.company.com/
   2. https://LEARNZ2.company.com/
   3. https://LEARNZ3.company.com/
   3. Allow for Course Level Admin Users, accessing from the Course Level, to view a list of all active student enrollments in their course.
   1. The list will be sorted alphabetically by Canvas Sortable Name.
   2. The list will include Students’ Canvas Sortable Names and SIS IDs.
   3. The list will be paginated at 50 records per page.
   4. The list of active students in the course will be retrieved when the Course Level Admin User accesses the launch point.
   4. Allow for Course Level Admin Users to select a student and establish a temporary Observer/Observee Account Links between the Course Level Admin User and the selected Student.
   1. Multiple Course Level Admin Users and Account Users can be temporarily linked to the same student, but only a single student can be linked to the Course Level Admin User at a time.
   1. Pre-existing Observer Enrollments or Account Links, those not created by the Solution, will not count towards this value and will not be visible nor affected by the Solution.
   2. Require Confirmation prior to link establishment.
   3. Once Confirmed, create an Account Level Observer/Observee Link between the Course Level Admin User and the selected Student.
   1. Establish the link at the Parent Consortium Instance: https://LEARNZ.company.com/
   4. Once a link has been established, store the timestamp of the link establishment as well as the affected users.
   5. Make the Details of the Solution Established Link available to all instances of the Solution across all 4 environments (Parent Consortium and Child Instances)
   6. Hide the list of students for the Course Level Admin User and in its place display details regarding the currently established link.
   1. This includes situations where the Course Level Admin User accesses the application in a different course or instance than the one in which they created the temporary link.
   7. Allow for the currently active link that was generated within the Solution to be ended by the user.  When ended by the user, show again the list of active students in the course.
   1. This includes situations where the Course Level Admin User accesses the application in a different instance than the one in which they created the temporary link.
   8. After the link has been established for one hour, remove the Observer/Observee Account Link between the Course Level Admin User and the student.  Upon access, show again the list of active students in the course.
   1. Clear the details of the Solution Established Link within all instances of the Solution across all 4 environments (Parent Consortium and Child Instances) and within any of the user’s courses.
   5. Allow for Account Level Users (i.e. Customer Partner School Administrators), accessing from the Sub-Account Level, to view a list of all active students within their sub-account.
   1. An active student in the sub-account is a student with at least one active student enrollment in a course within their sub-account.
   2. The list will be sorted alphabetically by Canvas Sortable Name.
   3. The list will include Students’ Canvas Sortable Names and SIS IDs.
   4. The list will be paginated at 50 records per page.
   5. The list can be filtered via Search Functionality.  When at least three characters have been entered, filter the results to students whose Canvas Sortable Name or SIS ID includes the entered characters.  If no matching students are found, display results indicating so.  Allow for the clearing of the search filter.
   6. Allow for Account Level Users (i.e. Customer Partner School Administrators) to select a student and establish a temporary Observer/Observee Account Links between the  Account Level Users and the selected Student.
   1. Multiple Course Level Admin User and Account Level Users can be temporarily linked to the same student.
   1. Pre-existing Observer Enrollments or Account Links, those not created by the Solution, will not count towards this value and will not be visible nor affected by the Solution.
   2. Require Confirmation prior to link establishment.
   3. Once Confirmed, create an Account Level Observer/Observee Link between the  Account Level Users and the selected Student.
   1. Establish the link at the Parent Consortium Instance: https://LEARNZ.company.com/
   4. Once a link has been established, store the timestamp of the link establishment as well as the affected users.
   5. Make the Details of the Solution Established Link available to all instances of the Solution across all 4 environments (Parent Consortium and Child Instances).
   6. Hide the list of students for the Account Level Users and in its place display details regarding the currently established link.
   1. This includes situations where the user accesses the application in a different sub-account or instance than the one in which they created the temporary link.
   7. Allow for the currently active link that was generated within the Solution to be ended by the user.  When ended by the user, show again the list of active students in the sub-account.
   1. This includes situations where the user accesses the application in a different sub-account or instance than the one in which they created the temporary link.
   8. After the link has been established for one hour, remove the Observer/Observee Account Link between the Account Level Users and the student.  Upon access, show again the list of active students in the sub-account launch for the user.
   1. Clear the details of the Solution Established Link within all instances of the Solution across all 4 environments (Parent Consortium and Child Instances).
   7. Add to the regular update sync process a job that clears any unsuccessful automatic link removals.
   8. Allow for users currently viewing the currently established link details to renew the link one time.  Once renewed, reset the hour timer, and do not remove the link until after this new timer has elapsed.
   9. Allow for users with existing account-level observer/observee links to students create a temporary link to a thusly linked student within the Solution, to allow for the viewing of the Student’s Canvas Calendar (see Milestone 3).  When this link’s hour expires (including any renewals), the account-level observer/observee link itself will not be deleted, but for the purposes of the Solution, that link will no longer be considered valid for Calendar viewing purposes.
________________
Milestone 3: Student Calendar Viewer
USER STORIES INCLUDED
	      5. As a Course Level Administrator Role, I can access a view of the selected student’s Canvas calendar, so that I can better understand their workload when assigning content or when attempting to meet with them.
      6. As an Account Level Role, I can access a view of the selected student’s Canvas calendar, so that I can better understand their workload when assigning content or when attempting to meet with them.
	SUMMARY
	The ability to access a read-only view of the Canvas Calendar for a currently-linked student will be provided to Solution Users.


company Requirements
      1. Add Functionality within the Solution for users who have created a temporary Observer/Observee link via the Solution, to see the Canvas Calendar for the selected student within the Solution.
      1. The Calendar View will be read only.  No events will be able to be created, deleted, or edited from within the Solution Calendar View.
      2. Access to the Solution Calendar view requires a temporary Observer/Observee Account Link have been established within the Solution and still be valid at the time of Calendar Viewing.
      1. While accessing the Solution Calendar view, display the linked Student’s Name and SIS ID.
      2. Allow for the user to renew the current link.  This renewal is limited to the same single use as that within the Student List view, and if used in either location, renewal is prevented in the other.  Once renewed, reset the hour timer, and do not remove the link until after this new timer has elapsed.
      3. The Calendar View will include the Student’s personal Canvas Calendar and events, as well as up to 9 of the Student’s course and/or group Calendars.
      1. If the student has 9 or fewer combined course or group calendars, all additional calendars will be set to display by default. These calendars can be disabled and enabled as needed.
      2. If the student has 10 or more combined course or group calendars, only the student’s personal calendar will be set to display by default, but other calendars can be enabled or disabled as needed.
      3. The calendar view will match the default Canvas Calendar view, with the exception that no actions can be taken on the Calendar and the view is read-only.
      1. See the Canvas Guides for Calendar Functionality:
      1. https://community.canvaslms.com/t5/Canvas-Basics-Guide/How-do-I-use-the-Calendar/ta-p/617613
      2. The Find Appoint functionality will not be included within the Solution Calendar Views as Solution Calendar Views are read-only.
________________
SCHEDULE 2
User Access
The following table defines user access to the application. Access definition for course roles (i.e. teacher, TA, designer, student, observer) include all custom roles created in Canvas that are based on the standard Canvas role. All administrator roles in Canvas are treated equally for external tool access.


ROLE
	LEVEL
	CAN ACCESS
	LAUNCH POINT
	Administrator
	ACCOUNT
	Yes
	Account Navigation
	Teacher
	COURSE ADMIN
	Yes
	Course Navigation
	TA
	COURSE ADMIN
	Yes
	Course Navigation
	Designer
	COURSE ADMIN
	Yes
	Course Navigation
	Student
	COURSE
	No
	N/A
	Observer
	COURSE
	No
	N/A
	________________


SCHEDULE 3
Observer Access in Canvas
The following information is taken from the Canvas Guides.  For the most up-to-date information, please access the “What is the Observer role?” guide, located at: https://community.canvaslms.com/t5/Canvas-Basics-Guide/What-is-the-Observer-role/ta-p/4


Observers can
      * View and read announcements
      * View assignments index page
      * View the calendar
      * Join conferences, if invited
      * Join collaborations, if invited
      * View personal inbox
      * Send conversation messages to instructor and student they are observing
      * View the dashboard
      * View and read discussions
      * View files unless they are locked
      * View grades, view assignment due dates and comments, and print grades
      * View modules and see due dates and point values
      * View pages and contribute if the instructor enables the Anyone can edit it setting
      * View profile pictures, if available
      * View syllabus
      * View outcomes
      * View quizzes index page
Observers cannot
      * Comment on announcements or discussions
      * Submit assignments or quizzes
      * View course rosters
      * Send conversation messages to students in the course they are not observing
      * View locked files or folders
      * Join groups
      * View unpublished courses
      * Access the Chat tool
      * Reserve appointment slots in the Scheduler
      * View grade audit trail
________________
Observer Limitations
      * Observers can view the same content as the student they are observing unless the content is unpublished or restricted by locked dates.
      * Observers can view module content locked by prerequisites or requirements, as module progression is not measured for observers.
      * Observers can view assignments even if the student they are observing has not completed them unless the assignments are locked.
      * Each instructor can customize the links in the Course Navigation and limit the number of features that students and observers can see. Observers may or may not see links to certain features.
      * Courses restricted to term dates are accessible to observers during the term dates. Student access dates do not apply to observers.
________________
SCHEDULE 4
Observer Visibility and Participation
The following information is taken from the Canvas Guides.  For the most up-to-date information, please access the “Observer Visibility and Participation” guide, located at: https://community.canvaslms.com/t5/Canvas-Resource-Documents/Observer-Visibility-and-Participation/ta-p/387091
Observer permissions are limited. Unauthorized warnings will appear if Observers click to a page that they do not have permission to access.
Each Instructor can customize the links in the Course Navigation and limit the number of features that Students and Observers can see. If the course navigation link does not appear, the instructor has hidden the link from course users. Observers may or may not have access to the features mentioned below. Observers cannot view a course until it is published.
Feature
	Observer Visibility
	Observer Participation
	Announcements
	✔️
Can view and read the announcements.
	✖️
Cannot comment or participate.
	Assignments
	✔️
Can view the assignments index page.
	✖️
Cannot submit assignments.

Calendar
	✔️
Can view personal calendar and calendars of the courses they are enrolled.
	✔️
Can add events to personal calendar or course calendar (with appropriate permission).
	✖️
Cannot view the personal calendar of student(s) they are observing[6] or courses they are not enrolled.
	✖️
Cannot add assignments to course calendars.
	Collaborations
	✔️
Can view the collaborations page.
	✖️
Cannot view, join, or participate in a collaboration unless invited by instructor.
	Conferences
	✔️
Can view the conferences page.
	✖️
Cannot view, join, or participate in a conference unless invited by instructor.

Conversations
	✔️
Can view own Inbox.
	✔️
Can send messages to the instructor and student they are observing.
	✖️
Cannot view messages sent to the student they are observing.
	✖️
Cannot send messages to others in the class.
	Discussions
	✔️
Can view discussions.
	✖️
Cannot comment or participate.
	Files
	✔️
Can preview and download some files.
	✖️
Cannot view a locked file or folder.
	Grades
	✔️
Can view grades including assignment comments for the student(s) they are observing. They can also filter the Grades view by grading period, filter by courses they are enrolled in, view assignment scores and due dates, and print grades.
	✖️
Cannot change grades.










	Groups
	✖️
Cannot view student groups.
	✖️
Cannot participate in student groups.
	Modules
	✔️
Can view the point values for individual assignments.
	✔️
Can click any link in modules.
	✖️
Cannot participate in Discussions, submit Assignments, or take Quizzes.
	Notifications
	✔️
Can view personal notification preferences.
	✔️
Currently receive notifications for assignment creation, assignment modification, created announcement, course invitation, added to a conversation (if included as recipient), appointments available for signup in Scheduler, linked student reserved appointment).
	Outcomes
	✔️
	✔️
	Pages
	✔️
Can view pages index page.
	✔️
Can edit if Anyone setting is enabled by instructor.
	People
	✖️
Cannot view course roster.
	✖️
	Quizzez
	✔️
Can view quizzes index page.
	✖️
Cannot take a quiz.
	✖️
Cannot view quiz.
	Rubrics
	✔️
Can view rubric.
	✖️
Cannot view graded results.
	Syllabus
	✔️
	✔️
	User Dashboard
	✔️
	✔️


Version: 2025.08.06                                                                                                                                   of
________________
[1] Number of estimated person-hours required to complete the requirements outlined in this SSD.
[2] Estimated cost at the hourly rate defined in Customer’s Statement of Work – Retainer Agreement. Final cost may be higher or lower, depending on the number of actual hours required to deliver the Solution. Excludes hosting, maintenance, and support fees, if applicable; see Fees for full pricing.
[3] Base estimate, after development begins, of business days to complete all project development work and testing; see Development Timeline for details.
[4] https://LEARNZ.company.com/, https://LEARNZ1.company.com/, https://LEARNZ2.company.com/, https://LEARNZ3.company.com/.
[5] The first annual maintenance invoice will be prorated to align with Customer’s Canvas subscription renewal.
[6] Viewing of Personal Calendars of Students is not available within Native Canvas functionality.  The Solution will provide a view of the Students Calendar, which will be accessible within the Solution’s application.
