inherit_from: .rubocop_todo.yml

AllCops:
  Exclude:
    - 'db/**/*'
    - 'node_modules/**/*'
    - 'config/**/*'
    - 'bin/**/*'
    - 'spec/rails_helper.rb'
  NewCops: enable
  SuggestExtensions: false

Style/FrozenStringLiteralComment:
  Enabled: true
Style/Documentation:
  Enabled: false
Style/ClassAndModuleChildren:
  Enabled: false
Style/SymbolArray:
  EnforcedStyle: brackets
Layout/LineLength:
  Max: 198
  Exclude:
    - 'spec/**/*'
Metrics/ClassLength:
  Max: 350
Metrics/CyclomaticComplexity:
  Max: 15
Metrics/ModuleLength:
  Max: 200
Metrics/MethodLength:
  Max: 30
Metrics/AbcSize:
  Enabled: false
Metrics/BlockLength:
  Max: 100
  Exclude:
    - 'spec/**/*'
Metrics/PerceivedComplexity:
  Max: 10
